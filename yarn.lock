# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@0no-co/graphql.web@npm:^1.0.5, @0no-co/graphql.web@npm:^1.0.8":
  version: 1.1.2
  resolution: "@0no-co/graphql.web@npm:1.1.2"
  peerDependencies:
    graphql: ^14.0.0 || ^15.0.0 || ^16.0.0
  peerDependenciesMeta:
    graphql:
      optional: true
  checksum: 10c0/7074de29681f0563cb9a90d702c7cda4443dce858e09f9a09adbafe32c302890cab81959ccba4ed7ac3e332423b2928a1dc95dd4a5004e6a5c156b733caa349a
  languageName: node
  linkType: hard

"@alloc/quick-lru@npm:^5.2.0":
  version: 5.2.0
  resolution: "@alloc/quick-lru@npm:5.2.0"
  checksum: 10c0/7b878c48b9d25277d0e1a9b8b2f2312a314af806b4129dc902f2bc29ab09b58236e53964689feec187b28c80d2203aff03829754773a707a8a5987f1b7682d92
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/sha256-js": "npm:^5.2.0"
    "@aws-crypto/supports-web-crypto": "npm:^5.2.0"
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-locate-window": "npm:^3.0.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/05f6d256794df800fe9aef5f52f2ac7415f7f3117d461f85a6aecaa4e29e91527b6fd503681a17136fa89e9dd3d916e9c7e4cfb5eba222875cb6c077bdc1d00d
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:5.2.0, @aws-crypto/sha256-js@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-js@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6c48701f8336341bb104dfde3d0050c89c288051f6b5e9bdfeb8091cf3ffc86efcd5c9e6ff2a4a134406b019c07aca9db608128f8d9267c952578a3108db9fd1
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/supports-web-crypto@npm:5.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/4d2118e29d68ca3f5947f1e37ce1fbb3239a0c569cc938cdc8ab8390d595609b5caf51a07c9e0535105b17bf5c52ea256fed705a07e9681118120ab64ee73af2
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/util@npm:5.2.0"
  dependencies:
    "@aws-sdk/types": "npm:^3.222.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0362d4c197b1fd64b423966945130207d1fe23e1bb2878a18e361f7743c8d339dad3f8729895a29aa34fff6a86c65f281cf5167c4bf253f21627ae80b6dd2951
  languageName: node
  linkType: hard

"@aws-sdk/client-cognito-identity@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/client-cognito-identity@npm:3.812.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/credential-provider-node": "npm:3.812.0"
    "@aws-sdk/middleware-host-header": "npm:3.804.0"
    "@aws-sdk/middleware-logger": "npm:3.804.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.804.0"
    "@aws-sdk/middleware-user-agent": "npm:3.812.0"
    "@aws-sdk/region-config-resolver": "npm:3.808.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.804.0"
    "@aws-sdk/util-user-agent-node": "npm:3.812.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/hash-node": "npm:^4.0.2"
    "@smithy/invalid-dependency": "npm:^4.0.2"
    "@smithy/middleware-content-length": "npm:^4.0.2"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-retry": "npm:^4.1.7"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.14"
    "@smithy/util-defaults-mode-node": "npm:^4.0.14"
    "@smithy/util-endpoints": "npm:^3.0.4"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6f45045d7f2aa1965ab5d369d64c52daa72a2a084be713e7b210baee197171f4a44a0c5c76aa547ec0381691fe3d75b8d5f8c62e56ba0da7751a14793623be79
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/client-sso@npm:3.812.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/middleware-host-header": "npm:3.804.0"
    "@aws-sdk/middleware-logger": "npm:3.804.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.804.0"
    "@aws-sdk/middleware-user-agent": "npm:3.812.0"
    "@aws-sdk/region-config-resolver": "npm:3.808.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.804.0"
    "@aws-sdk/util-user-agent-node": "npm:3.812.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/hash-node": "npm:^4.0.2"
    "@smithy/invalid-dependency": "npm:^4.0.2"
    "@smithy/middleware-content-length": "npm:^4.0.2"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-retry": "npm:^4.1.7"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.14"
    "@smithy/util-defaults-mode-node": "npm:^4.0.14"
    "@smithy/util-endpoints": "npm:^3.0.4"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1cce58f588ca9c73a02e86893dd4a540485e86acb9a46549431131209610dedd2b6f0c3f61d0586004b7a51045ad5766f1a3ae84eedc1c2c1e95e46ec50caf58
  languageName: node
  linkType: hard

"@aws-sdk/core@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/core@npm:3.812.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/signature-v4": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    fast-xml-parser: "npm:4.4.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a51e4fbf9f7d038539d187e4e41808874e1a2ac1f062e8dc697425e7e22ca714d0dfb2b18672c88e17582401bbfc52d7f58be9a00e96dcf76820c646ec75e66f
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-cognito-identity@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-provider-cognito-identity@npm:3.812.0"
  dependencies:
    "@aws-sdk/client-cognito-identity": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/817d4898e3191f1fea77082c6aa37376fc9778eb3c85270b2210459c7934cf1a090150f590ed77e319c5cd44073be0e53497c8fad543df7e3094a5b7e75aaabe
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.812.0"
  dependencies:
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/34267bcde5f8e40deeb3959858d4997f861f445467a96a67843138a0f470b9c33d4900e0577c87f4226d4189ae59908e8a8c4b7c4f2964cfd0e6523111ed2ffe
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-http@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-provider-http@npm:3.812.0"
  dependencies:
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-stream": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/fd1269b2f2efc3f909048bcb87d141aad478eed52e326c14924139b240097d931c5941bfd9214b08624ddd02e72ab85852f2d9b0e154dab79dbc0ba6cd598d01
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.812.0"
  dependencies:
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/credential-provider-env": "npm:3.812.0"
    "@aws-sdk/credential-provider-http": "npm:3.812.0"
    "@aws-sdk/credential-provider-process": "npm:3.812.0"
    "@aws-sdk/credential-provider-sso": "npm:3.812.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.812.0"
    "@aws-sdk/nested-clients": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/credential-provider-imds": "npm:^4.0.4"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/db6855158a5850dfddda748fd35d790ed901ff65e2569f6ee5a4841e0f7fc9b01249c805edb4aab8365196d8d361754d8154464696c1e72aafd57aece077be65
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.812.0"
  dependencies:
    "@aws-sdk/credential-provider-env": "npm:3.812.0"
    "@aws-sdk/credential-provider-http": "npm:3.812.0"
    "@aws-sdk/credential-provider-ini": "npm:3.812.0"
    "@aws-sdk/credential-provider-process": "npm:3.812.0"
    "@aws-sdk/credential-provider-sso": "npm:3.812.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/credential-provider-imds": "npm:^4.0.4"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/06b5fafdf5989b529f4f70f57747b31956a91b9582afc9fbf110cb52ca8d968eeab3b05e9e0baca83580d2629ddd78384ea7ede043802a9d937e07fe38f497aa
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.812.0"
  dependencies:
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b9a4a7bf3e74eec65da8992642c0378310765031f5992205f9a389ad1a165308a3da4808b486b55d6569571fedbafad6daf45b7829ec377018589b23bd08c556
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.812.0"
  dependencies:
    "@aws-sdk/client-sso": "npm:3.812.0"
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/token-providers": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0b27c63e3d3e92578ad2ea6899495359e70bda356d18363479bbabe1f3457ba53c2a6620261cc98491e5ba1feaf14b73eef062ed4cf0e1b5af3686493da60782
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.812.0"
  dependencies:
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/nested-clients": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b2afc8529665ac18c693211ff3834a0e1c5d0ebd8fc3a755f94ca6fcb5a45dbf4631ebb9b4e1509589edbe11705477f3fac1a12508ad8eacc8ad1e8ef69eba08
  languageName: node
  linkType: hard

"@aws-sdk/credential-providers@npm:^3.186.0":
  version: 3.812.0
  resolution: "@aws-sdk/credential-providers@npm:3.812.0"
  dependencies:
    "@aws-sdk/client-cognito-identity": "npm:3.812.0"
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/credential-provider-cognito-identity": "npm:3.812.0"
    "@aws-sdk/credential-provider-env": "npm:3.812.0"
    "@aws-sdk/credential-provider-http": "npm:3.812.0"
    "@aws-sdk/credential-provider-ini": "npm:3.812.0"
    "@aws-sdk/credential-provider-node": "npm:3.812.0"
    "@aws-sdk/credential-provider-process": "npm:3.812.0"
    "@aws-sdk/credential-provider-sso": "npm:3.812.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.812.0"
    "@aws-sdk/nested-clients": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/credential-provider-imds": "npm:^4.0.4"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6d124573b46b13942f60fb37e244ec81cb8469d3774d9157b048e4ade9d9f1354f2d8f25ea54d7f183b34a0f37f97ece59ee32135d98fccfeedec09a6130b027
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4089d24b70d2d80f4936e4468f69d3ce1dfd80bc84ed0db65c57b25814c7842076e6ab55a80346f2b396b1db967c447370839a2dac836e1db81b01928985ceeb
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-logger@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4b7deebb336231e529857673fbba898e578b7ca88049923f3e822e67732262a08bbcaecf388e1cd687102744ad9867ab535b71e8b086602f91e1a4bb43b39a5a
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/86e6d2902dd8ec8551d9f66ebc470b21bcf0d3caab457ef3395ee18fcd371d3816e86f17f39cd69a27cc39ee96b6b85d0b179e44fec5a6da11f44a4905d8ac92
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.812.0"
  dependencies:
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c96be4433ac7d40d40d7d70a559e7901c2af60c0c37955c60dcd39efb35e77eee568362a214271ab162e71cfc5ad90d865649f384d7facb9e89e3554ba664c6e
  languageName: node
  linkType: hard

"@aws-sdk/nested-clients@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/nested-clients@npm:3.812.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.812.0"
    "@aws-sdk/middleware-host-header": "npm:3.804.0"
    "@aws-sdk/middleware-logger": "npm:3.804.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.804.0"
    "@aws-sdk/middleware-user-agent": "npm:3.812.0"
    "@aws-sdk/region-config-resolver": "npm:3.808.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@aws-sdk/util-endpoints": "npm:3.808.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.804.0"
    "@aws-sdk/util-user-agent-node": "npm:3.812.0"
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/core": "npm:^3.3.3"
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/hash-node": "npm:^4.0.2"
    "@smithy/invalid-dependency": "npm:^4.0.2"
    "@smithy/middleware-content-length": "npm:^4.0.2"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-retry": "npm:^4.1.7"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.14"
    "@smithy/util-defaults-mode-node": "npm:^4.0.14"
    "@smithy/util-endpoints": "npm:^3.0.4"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/782ce81e76dcf3aa425885e0cba2bb4a3a7df1c5a4692ecb4a76131935cdf0a7ed8f598204bf1a058fe0c9dad8cf8c2193891b564b8e0436090974bcf36cb3e2
  languageName: node
  linkType: hard

"@aws-sdk/region-config-resolver@npm:3.808.0":
  version: 3.808.0
  resolution: "@aws-sdk/region-config-resolver@npm:3.808.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/3e21dcf467afda7cfe3298b94fa29e3df3440ba8ba2eddf796c8e2b88c77584ed80ea6ebc2044117bddd78bf53454043d0a42d4cad821ae87293cd1941823068
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/token-providers@npm:3.812.0"
  dependencies:
    "@aws-sdk/nested-clients": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a66c3dd4405a2e7d74450f2785d4891802977c13e127e2f2a0398d932f9c130403bb5ecf0a72827fe5aee54350a7906c6879e52ccc1d1a6a0dec2da4fc7291d2
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.804.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.804.0
  resolution: "@aws-sdk/types@npm:3.804.0"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/cdda6d77466ed34de1ca0e23b9df5c576e7d67dc87cfda2a2d024a9c5f4180fe77ebaf57194a4cf034ee5edfbcd8efdeca458e9b61b1f364b261284b4a141ae5
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.808.0":
  version: 3.808.0
  resolution: "@aws-sdk/util-endpoints@npm:3.808.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-endpoints": "npm:^3.0.4"
    tslib: "npm:^2.6.2"
  checksum: 10c0/197e07aa9715eb3930a5f2d38fd379687ba306d3025d2498a0a4fced77ed34ab013c25e8cfec9ce07a807a72e2086472883b4ea268047fe3865fad740740f471
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-locate-window@npm:3.804.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/a0ceaf6531f188751fea7e829b730650689fa2196e0b3f870dde3888bcb840fe0852e10488699d4d9683db0765cd7f7060ca8ac216348991996b6d794f9957ab
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/types": "npm:^4.2.0"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d459a94955e7298c72dc97e7e59d276320ff004615b117bd2f63bbacf762159c5476f40a8f24d9b5eb054915bd6ce97ffade3a649d5fad98643f92a9d90a5192
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.812.0":
  version: 3.812.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.812.0"
  dependencies:
    "@aws-sdk/middleware-user-agent": "npm:3.812.0"
    "@aws-sdk/types": "npm:3.804.0"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: 10c0/8a944bb83f514e90113357af712bcfed8f4fe88889ba25a162d7e4530dd0f835474d40a223779639ad096459766520ea50e1e29e714400f2d65abdb9ee704892
  languageName: node
  linkType: hard

"@babel/code-frame@npm:7.10.4, @babel/code-frame@npm:~7.10.4":
  version: 7.10.4
  resolution: "@babel/code-frame@npm:7.10.4"
  dependencies:
    "@babel/highlight": "npm:^7.10.4"
  checksum: 10c0/69e0f52986a1f40231d891224f420436629b6678711b68c088e97b7bdba1607aeb5eb9cfb070275c433f0bf43c37c134845db80d1cdbf5ac88a69b0bdcce9402
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.24.7, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/compat-data@npm:7.27.2"
  checksum: 10c0/077c9e01af3b90decee384a6a44dcf353898e980cee22ec7941f9074655dbbe97ec317345536cdc7ef7391521e1497930c522a3816af473076dd524be7fccd32
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3, @babel/core@npm:^7.20.0, @babel/core@npm:^7.21.3, @babel/core@npm:^7.23.0, @babel/core@npm:^7.23.9, @babel/core@npm:^7.25.2":
  version: 7.27.1
  resolution: "@babel/core@npm:7.27.1"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helpers": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/0fc31f87f5401ac5d375528cb009f4ea5527fc8c5bb5b64b5b22c033b60fd0ad723388933a5f3f5db14e1edd13c958e9dd7e5c68f9b68c767aeb496199c8a4bb
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.20.5, @babel/generator@npm:^7.25.0, @babel/generator@npm:^7.26.5, @babel/generator@npm:^7.27.1, @babel/generator@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/generator@npm:7.27.1"
  dependencies:
    "@babel/parser": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/c4156434b21818f558ebd93ce45f027c53ee570ce55a84fd2d9ba45a79ad204c17e0bff753c886fb6c07df3385445a9e34dc7ccb070d0ac7e80bb91c8b57f423
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/fc4751b59c8f5417e1acb0455d6ffce53fa5e79b3aca690299fbbf73b1b65bfaef3d4a18abceb190024c5836bb6cfbc3711e83888648df93df54e18152a1196c
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4ee199671d6b9bdd4988aa2eea4bdced9a73abfc831d81b00c7634f49a8fc271b3ceda01c067af58018eb720c6151322015d463abea7072a368ee13f35adbb4c
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/591fe8bd3bb39679cc49588889b83bd628d8c4b99c55bafa81e80b1e605a348b64da955e3fd891c4ba3f36fd015367ba2eadea22af6a7de1610fbb5bcc2d3df0
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.3, @babel/helper-define-polyfill-provider@npm:^0.6.4":
  version: 0.6.4
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.4"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b74f2b46e233a178618d19432bdae16e0137d0a603497ee901155e083c4a61f26fe01d79fb95d5f4c22131ade9d958d8f587088d412cca1302633587f070919d
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/5762ad009b6a3d8b0e6e79ff6011b3b8fdda0fefad56cfa8bfbe6aa02d5a8a8a9680a45748fe3ac47e735a03d2d88c0a676e3f9f59f20ae9fadcc8d51ccd5a53
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.22.15, @babel/helper-module-imports@npm:^7.25.9, @babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-transforms@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/196ab29635fe6eb5ba6ead2972d41b1c0d40f400f99bd8fc109cef21440de24c26c972fabf932585e618694d590379ab8d22def8da65a54459d38ec46112ead7
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/6b861e7fcf6031b9c9fc2de3cd6c005e94a459d6caf3621d93346b52774925800ca29d4f64595a5ceacf4d161eb0d27649ae385110ed69491d9776686fa488e6
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.27.1, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-wrap-function": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/5ba6258f4bb57c7c9fa76b55f416b2d18c867b48c1af4f9f2f7cd7cc933fe6da7514811d08ceb4972f1493be46f4b69c40282b811d1397403febae13c2ec57b5
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4f2eaaf5fcc196580221a7ccd0f8873447b5d52745ad4096418f6101a1d2e712e9f93722c9a32bc9769a1dc197e001f60d6f5438d4dfde4b9c6a9e4df719354c
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/f625013bcdea422c470223a2614e90d2c1cc9d832e97f32ca1b4f82b34bb4aa67c3904cb4b116375d3b5b753acfb3951ed50835a1e832e7225295c7b0c24dff7
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9, @babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/c472f75c0951bc657ab0a117538c7c116566ae7579ed47ac3f572c42dc78bd6f1e18f52ebe80d38300c991c3fcaa06979e2f8864ee919369dabd59072288de30
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helpers@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e078257b9342dae2c041ac050276c5a28701434ad09478e6dc6976abd99f721a5a92e4bebddcbca6b1c3a7e8acace56a946340c701aad5e7507d2c87446459ba
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.10.4":
  version: 7.25.9
  resolution: "@babel/highlight@npm:7.25.9"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.25.9"
    chalk: "npm:^2.4.2"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/ae0ed93c151b85a07df42936117fa593ce91563a22dfc8944a90ae7088c9679645c33e00dcd20b081c1979665d65f986241172dae1fc9e5922692fc3ff685a49
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.23.0, @babel/parser@npm:^7.23.9, @babel/parser@npm:^7.25.3, @babel/parser@npm:^7.26.7, @babel/parser@npm:^7.27.1, @babel/parser@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/parser@npm:7.27.2"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/3c06692768885c2f58207fc8c2cbdb4a44df46b7d93135a083f6eaa49310f7ced490ce76043a2a7606cdcc13f27e3d835e141b692f2f6337a2e7f43c1dbb04b4
  languageName: node
  linkType: hard

"@babel/plugin-proposal-decorators@npm:^7.12.9":
  version: 7.27.1
  resolution: "@babel/plugin-proposal-decorators@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-decorators": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/3af0db6b2468907bcaf62246b2cfd3616ba9239ea1cd26036ec6baff1bc095fe4964853b1d29a79944d36e6e3d331cd130d05b0c41c835266daf7bb9d8e8f87c
  languageName: node
  linkType: hard

"@babel/plugin-proposal-export-default-from@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-proposal-export-default-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6e0756e0692245854028caea113dad2dc11fcdd479891a59d9a614a099e7e321f2bd25a1e3dd6f3b36ba9506a76f072f63adbf676e5ed51e7eeac277612e3db2
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d13efb282838481348c71073b6be6245b35d4f2f964a8f71e4174f235009f929ef7613df25f8d2338e2d3e44bc4265a9f8638c6aaa136d7a61fe95985f9725c8
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/686891b81af2bc74c39013655da368a480f17dd237bf9fbc32048e5865cb706d5a8f65438030da535b332b1d6b22feba336da8fa931f663b6b34e13147d12dde
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.12.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/95168fa186416195280b1264fb18afcdcdcea780b3515537b766cb90de6ce042d42dd6a204a39002f794ae5845b02afb0fd4861a3308a861204a55e68310a120
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4464bf9115f4a2d02ce1454411baf9cfb665af1da53709c5c56953e5e2913745b0fcce82982a00463d6facbdd93445c691024e310b91431a1e2f024b158f6371
  languageName: node
  linkType: hard

"@babel/plugin-syntax-decorators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-decorators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/46ef933bae10b02a8f8603b2f424ecbe23e134a133205bee7c0902dae3021c183a683964cab41ea5433820aa05be0f6f36243551f68a1d94e02ac082cec87aa1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9c50927bf71adf63f60c75370e2335879402648f468d0172bc912e303c6a3876927d8eb35807331b57f415392732ed05ab9b42c68ac30a936813ab549e0246c5
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-default-from@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-export-default-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9aa62f5916950f3e5f91657895f4635b1c77e108e453ef12c30dc7670c3441bdd65cd28be20d6ddc9003ed471cc98465785a14cd76c61f077c1c84264f1f28ca
  languageName: node
  linkType: hard

"@babel/plugin-syntax-flow@npm:^7.12.1, @babel/plugin-syntax-flow@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-flow@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4d34ca47044398665cbe0293baea7be230ca4090bc7981ffba5273402a215c95976c6f811c7b32f10b326cc6aab6886f26c29630c429aa45c3f350c5ccdfdbbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e66f7a761b8360419bbb93ab67d87c8a97465ef4637a985ff682ce7ba6918b34b29d81190204cf908d0933058ee7b42737423cd8a999546c21b3aabad4affa9a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0b08b5e4c3128523d8e346f8cfc86824f0da2697b1be12d71af50a31aff7a56ceb873ed28779121051475010c28d6146a6bfea8518b150b71eeb4e46190172ee
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e98f31b2ec406c57757d115aac81d0336e8434101c224edd9a5c93cefa53faf63eacc69f3138960c8b25401315af03df37f68d316c151c4b933136716ed6906e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.27.1, @babel/plugin-syntax-jsx@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bc5afe6a458d5f0492c02a54ad98c5756a0c13bd6d20609aae65acd560a9e141b0876da5f358dce34ea136f271c1016df58b461184d7ae9c4321e0f98588bc84
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2594cfbe29411ad5bc2ad4058de7b2f6a8c5b86eda525a993959438615479e59c012c14aec979e538d60a584a1a799b60d1b8942c3b18468cb9d99b8fd34cd0b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2024fbb1162899094cfc81152449b12bd0cc7053c6d4bda8ac2852545c87d0a851b1b72ed9560673cbf3ef6248257262c3c04aabf73117215c1b9cc7dd2542ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c55a82b3113480942c6aa2fcbe976ff9caa74b7b1109ff4369641dfbc88d1da348aceb3c31b6ed311c84d1e7c479440b961906c735d0ab494f688bf2fd5b9bb9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/ee1eab52ea6437e3101a0a7018b0da698545230015fc8ab129d292980ec6dff94d265e9e90070e8ae5fed42f08f1622c14c94552c77bcac784b37f503a82ff26
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/27e2493ab67a8ea6d693af1287f7e9acec206d1213ff107a928e85e173741e1d594196f99fec50e9dde404b09164f39dec5864c767212154ffe1caa6af0bc5af
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/46edddf2faa6ebf94147b8e8540dfc60a5ab718e2de4d01b2c0bdf250a4d642c2bd47cbcbb739febcb2bf75514dbcefad3c52208787994b8d0f8822490f55e81
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/69822772561706c87f0a65bc92d0772cea74d6bc0911537904a676d5ff496a6d3ac4e05a166d8125fce4a16605bace141afc3611074e170a994e66e5397787f3
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/14bf6e65d5bc1231ffa9def5f0ef30b19b51c218fcecaa78cd1bdf7939dfdf23f90336080b7f5196916368e399934ce5d581492d8292b46a2fb569d8b2da106f
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1, @babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/11589b4c89c66ef02d57bf56c6246267851ec0c361f58929327dc3e070b0dab644be625bbe7fb4c4df30c3634bfdfe31244e1f517be397d2def1487dbbe3c37d
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.0.0-0, @babel/plugin-transform-arrow-functions@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/19abd7a7d11eef58c9340408a4c2594503f6c4eaea1baa7b0e5fbdda89df097e50663edb3448ad2300170b39efca98a75e5767af05cad3b0facb4944326896a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.25.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/772e449c69ee42a466443acefb07083bd89efb1a1d95679a4dc99ea3be9d8a3c43a2b74d2da95d7c818e9dd9e0b72bfa7c03217a1feaf108f21b7e542f0943c0
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e76b1f6f9c3bbf72e17d7639406d47f09481806de4db99a8de375a0bb40957ea309b20aa705f0c25ab1d7c845e3f365af67eafa368034521151a0e352a03ef2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.25.0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoping@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d3f357beeb92fbdf3045aea2ba286a60dafc9c2d2a9f89065bb3c4bea9cc48934ee6689df3db0439d9ec518eda5e684f3156cab792b7c38c33ece2f8204ddee8
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.0.0-0, @babel/plugin-transform-class-properties@npm:^7.22.5, @babel/plugin-transform-class-properties@npm:^7.25.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cc0662633c0fe6df95819fef223506ddf26c369c8d64ab21a728d9007ec866bf9436a253909819216c24a82186b6ccbc1ec94d7aaf3f82df227c7c02fa6a704b
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.0.0-0, @babel/plugin-transform-classes@npm:^7.25.4":
  version: 7.27.1
  resolution: "@babel/plugin-transform-classes@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1071f4cb1ed5deb5e6f8d0442f2293a540cac5caa5ab3c25ad0571aadcbf961f61e26d367a67894976165a543e02f3a19e40b63b909afbed6e710801a590635c
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e09a12f8c8ae0e6a6144c102956947b4ec05f6c844169121d0ec4529c2d30ad1dc59fee67736193b87a402f44552c888a519a680a31853bdb4d34788c28af3b0
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.24.8, @babel/plugin-transform-destructuring@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-destructuring@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/56afda7a0b205f8d1af727daef4c529fc2e756887408affd39033ae4476e54d586d3d9dc1e72cfb15c74a2a5ca0653ab13dbaa8cbf79fbb2a3a746d0f107cb86
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.25.9":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d7165cad11f571a54c8d9263d6c6bf2b817aff4874f747cb51e6e49efb32f2c9b37a6850cdb5e3b81e0b638141bb77dc782a6ec1a94128859fbdf7767581e07c
  languageName: node
  linkType: hard

"@babel/plugin-transform-flow-strip-types@npm:^7.25.2, @babel/plugin-transform-flow-strip-types@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-flow-strip-types@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-flow": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c61c43244aacdcd479ad9ba618e1c095a5db7e4eadc3d19249602febc4e97153230273c014933f5fe4e92062fa56dab9bed4bc430197d5b2ffeb2158a4bf6786
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4635763173a23aae24480681f2b0996b4f54a0cb2368880301a1801638242e263132d1e8adbe112ab272913d1d900ee0d6f7dea79443aef9d3325168cd88b3fb
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.25.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5abdc7b5945fbd807269dcc6e76e52b69235056023b0b35d311e8f5dfd6c09d9f225839798998fc3b663f50cf701457ddb76517025a0d7a5474f3fe56e567a4c
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.25.2":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c40dc3eb2f45a92ee476412314a40e471af51a0f51a24e91b85cef5fc59f4fe06758088f541643f07f949d2c67ee7bdce10e11c5ec56791ae09b15c3b451eeca
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5b0abc7c0d09d562bf555c646dce63a30288e5db46fd2ce809a61d064415da6efc3b2b3c59b8e4fe98accd072c89a2f7c3765b400e4bf488651735d314d9feeb
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.23.0, @babel/plugin-transform-modules-commonjs@npm:^7.24.8, @babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4def972dcd23375a266ea1189115a4ff61744b2c9366fc1de648b3fab2c650faf1a94092de93a33ff18858d2e6c4dddeeee5384cb42ba0129baeab01a5cdf1e2
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/8eaa8c9aee00a00f3bd8bd8b561d3f569644d98cb2cfe3026d7398aabf9b29afd62f24f142b4112fa1f572d9b0e1928291b099cde59f56d6b59f4d565e58abf2
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.0.0-0, @babel/plugin-transform-nullish-coalescing-operator@npm:^7.22.11, @babel/plugin-transform-nullish-coalescing-operator@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a435fc03aaa65c6ef8e99b2d61af0994eb5cdd4a28562d78c3b0b0228ca7e501aa255e1dff091a6996d7d3ea808eb5a65fd50ecd28dfb10687a8a1095dcadc7a
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b72cbebbfe46fcf319504edc1cf59f3f41c992dd6840db766367f6a1d232cd2c52143c5eaf57e0316710bee251cae94be97c6d646b5022fcd9274ccb131b470c
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.24.7":
  version: 7.27.2
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.27.2"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.27.1"
    "@babel/plugin-transform-parameters": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e255b262dd65c8700078d9f6ed87bd45f951a905dda6b3414be28d7b2781b18e6b812e9d71421e61360c9cf51e1e619c1d48348593bb7399496f61f5f221446
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/807a4330f1fac08e2682d57bc82e714868fc651c8876f9a8b3a3fd8f53c129e87371f8243e712ac7dae11e090b737a2219a02fe1b6459a29e664fa073c3277bb
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.0.0-0, @babel/plugin-transform-optional-chaining@npm:^7.23.0, @babel/plugin-transform-optional-chaining@npm:^7.24.8":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5b18ff5124e503f0a25d6b195be7351a028b3992d6f2a91fb4037e2a2c386400d66bc1df8f6df0a94c708524f318729e81a95c41906e5a7919a06a43e573a525
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.24.7, @babel/plugin-transform-parameters@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-parameters@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/453a9618735eeff5551d4c7f02c250606586fe1dd210ec9f69a4f15629ace180cd944339ebff2b0f11e1a40567d83a229ba1c567620e70b2ebedea576e12196a
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.22.5, @babel/plugin-transform-private-methods@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/232bedfe9d28df215fb03cc7623bdde468b1246bdd6dc24465ff4bf9cc5f5a256ae33daea1fafa6cc59705e4d29da9024bb79baccaa5cd92811ac5db9b9244f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a8c4536273ca716dcc98e74ea25ca76431528554922f184392be3ddaf1761d4aa0e06f1311577755bd1613f7054fb51d29de2ada1130f743d329170a1aa1fe56
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.24.7, @babel/plugin-transform-react-display-name@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-display-name@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6cd474b5fb30a2255027d8fc19975aee1c1da54dd8bc8b79802676096182ca4136302ce65a24fbb277f8fe30f266006bbf327ef6be2846d3681eb57509744125
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.27.1"
  dependencies:
    "@babel/plugin-transform-react-jsx": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/eb8c4b6a79dc5c49b41e928e2037e1ee0bbfa722e4fd74c0b7c0d11103c82c2c25c434000e1b051d534c7261ab5c92b6d1e85313bf1b26e37db3f051ae217b58
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00a4f917b70a608f9aca2fb39aabe04a60aa33165a7e0105fd44b3a8531630eb85bf5572e9f242f51e6ad2fa38c2e7e780902176c863556c58b5ba6f6e164031
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e67b56c39c4d03e59e03ba80692b24c5a921472079b63af711b1d250fc37c1733a17069b63537f750f3e937ec44a42b1ee6a46cd23b1a0df5163b17f741f7f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.25.2, @babel/plugin-transform-react-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1a08637c39fc78c9760dd4a3ed363fdbc762994bf83ed7872ad5bda0232fcd0fc557332f2ce36b522c0226dfd9cc8faac6b88eddda535f24825198a689e571af
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/34bc090f4a7e460d82a851971b4d0f32e4bb519bafb927154f4174506283fe02b0f471fc20655c6050a8bf7b748bfa31c7e8f7d688849476d8266623554fbb28
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-regenerator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/42395908899310bb107d9ca31ebd4c302e14c582e3ad3ebfe1498fabafc43155c8f10850265c1e686a2afcf50d1f402cc5c5218fba72e167852607a4d8d6492e
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-runtime@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    babel-plugin-polyfill-corejs2: "npm:^0.4.10"
    babel-plugin-polyfill-corejs3: "npm:^0.11.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/7abbae60a6441ba8546dee3fcbc00b38038304250ba2419adaf0c76267bff43420ff75b7049003a24a829e01d9fde2ac8a422352af6d88aebd31996a83f04c2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.0.0-0, @babel/plugin-transform-shorthand-properties@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bd5544b89520a22c41a6df5ddac9039821d3334c0ef364d18b0ba9674c5071c223bcc98be5867dc3865cb10796882b7594e2c40dedaff38e1b1273913fe353e1
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b34fc58b33bd35b47d67416655c2cbc8578fbb3948b4592bc15eb6d8b4046986e25c06e3b9929460fa4ab08e9653582415e7ef8b87d265e1239251bdf5a4c162
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5698df2d924f0b1b7bdb7ef370e83f99ed3f0964eb3b9c27d774d021bee7f6d45f9a73e2be369d90b4aff1603ce29827f8743f091789960e7669daf9c3cda850
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.0.0-0":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c90f403e42ef062b60654d1c122c70f3ec6f00c2f304b0931ebe6d0b432498ef8a5ef9266ddf00debc535f8390842207e44d3900eff1d2bab0cc1a700f03e083
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.25.2, @babel/plugin-transform-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/48f1db5de17a0f9fc365ff4fb046010aedc7aad813a7aa42fb73fcdab6442f9e700dde2cc0481086e01b0dae662ae4d3e965a52cde154f0f146d243a8ac68e93
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.0.0-0, @babel/plugin-transform-unicode-regex@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6abda1bcffb79feba6f5c691859cdbe984cc96481ea65d5af5ba97c2e843154005f0886e25006a37a2d213c0243506a06eaeafd93a040dbe1f79539016a0d17a
  languageName: node
  linkType: hard

"@babel/preset-flow@npm:^7.22.15":
  version: 7.27.1
  resolution: "@babel/preset-flow@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-transform-flow-strip-types": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/252216c91ba3cc126f10c81c1df495ef2c622687d17373bc619354a7fb7280ea83f434ed1e7149dbddd712790d16ab60f5b864d007edd153931d780f834e52c1
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.22.15":
  version: 7.27.1
  resolution: "@babel/preset-react@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-transform-react-display-name": "npm:^7.27.1"
    "@babel/plugin-transform-react-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-react-jsx-development": "npm:^7.27.1"
    "@babel/plugin-transform-react-pure-annotations": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a80b02ef08b026cb9830d6512d08c7cd378eef4c0631dacba4aa1106240d9bb76af6373463f0255f4bbdbfcce40375a61e92735375906ba5871629b0c314bc45
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.16.7, @babel/preset-typescript@npm:^7.17.12, @babel/preset-typescript@npm:^7.23.0":
  version: 7.27.1
  resolution: "@babel/preset-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cba6ca793d915f8aff9fe2f13b0dfbf5fd3f2e9a17f17478ec9878e9af0d206dcfe93154b9fd353727f16c1dca7c7a3ceb4943f8d28b216235f106bc0fbbcaa3
  languageName: node
  linkType: hard

"@babel/register@npm:^7.22.15":
  version: 7.27.1
  resolution: "@babel/register@npm:7.27.1"
  dependencies:
    clone-deep: "npm:^4.0.1"
    find-cache-dir: "npm:^2.0.0"
    make-dir: "npm:^2.1.0"
    pirates: "npm:^4.0.6"
    source-map-support: "npm:^0.5.16"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9584f6c5d980aa7eb6f56f56dfc12fa01a47ab11d542908192cb455a5249d489ab24efcd5de7c1b8be0fb47cd5594e4ee5652c58ba9b857fb81e783541c6a0ff
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.18.6, @babel/runtime@npm:^7.20.0, @babel/runtime@npm:^7.25.0, @babel/runtime@npm:^7.8.7":
  version: 7.27.1
  resolution: "@babel/runtime@npm:7.27.1"
  checksum: 10c0/530a7332f86ac5a7442250456823a930906911d895c0b743bf1852efc88a20a016ed4cd26d442d0ca40ae6d5448111e02a08dd638a4f1064b47d080e2875dc05
  languageName: node
  linkType: hard

"@babel/template@npm:^7.25.0, @babel/template@npm:^7.27.1, @babel/template@npm:^7.3.3":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse--for-generate-function-map@npm:@babel/traverse@^7.25.3, @babel/traverse@npm:^7.23.0, @babel/traverse@npm:^7.25.3, @babel/traverse@npm:^7.26.7, @babel/traverse@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/traverse@npm:7.27.1"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/d912110037b03b1d70a2436cfd51316d930366a5f54252da2bced1ba38642f644f848240a951e5caf12f1ef6c40d3d96baa92ea6e84800f2e891c15e97b25d50
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.21.3, @babel/types@npm:^7.23.0, @babel/types@npm:^7.25.2, @babel/types@npm:^7.26.7, @babel/types@npm:^7.27.1, @babel/types@npm:^7.3.3":
  version: 7.27.1
  resolution: "@babel/types@npm:7.27.1"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/ed736f14db2fdf0d36c539c8e06b6bb5e8f9649a12b5c0e1c516fed827f27ef35085abe08bf4d1302a4e20c9a254e762eed453bce659786d4a6e01ba26a91377
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 10c0/6b80ae4cb3db53f486da2dc63b6e190a74c8c3cca16bb2733f234a0b6a9382b09b146488ae08e2b22cf00f6c83e20f3e040a2f7894f05c045c946d6a090b1d52
  languageName: node
  linkType: hard

"@callstack/react-theme-provider@npm:^3.0.9":
  version: 3.0.9
  resolution: "@callstack/react-theme-provider@npm:3.0.9"
  dependencies:
    deepmerge: "npm:^3.2.0"
    hoist-non-react-statics: "npm:^3.3.0"
  peerDependencies:
    react: ">=16.3.0"
  checksum: 10c0/ac3463383c35e1a4ef813e869532bf86d3e83a4c211b00616c832a5f08545f04f07f769726664b5010124575af2cf8152aabfb7d4c37a64b22ae8a7f9490df0e
  languageName: node
  linkType: hard

"@egjs/hammerjs@npm:^2.0.17":
  version: 2.0.17
  resolution: "@egjs/hammerjs@npm:2.0.17"
  dependencies:
    "@types/hammerjs": "npm:^2.0.36"
  checksum: 10c0/dbedc15a0e633f887c08394bd636faf6a3abd05726dc0909a0e01209d5860a752d9eca5e512da623aecfabe665f49f1d035de3103eb2f9022c5cea692f9cc9be
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:1.2.2":
  version: 1.2.2
  resolution: "@emotion/is-prop-valid@npm:1.2.2"
  dependencies:
    "@emotion/memoize": "npm:^0.8.1"
  checksum: 10c0/bb1530dcb4e0e5a4fabb219279f2d0bc35796baf66f6241f98b0d03db1985c890a8cafbea268e0edefd5eeda143dbd5c09a54b5fba74cee8c69b98b13194af50
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.8.1":
  version: 0.8.1
  resolution: "@emotion/memoize@npm:0.8.1"
  checksum: 10c0/dffed372fc3b9fa2ba411e76af22b6bb686fb0cb07694fdfaa6dd2baeb0d5e4968c1a7caa472bfcf06a5997d5e7c7d16b90e993f9a6ffae79a2c3dbdc76dfe78
  languageName: node
  linkType: hard

"@emotion/unitless@npm:0.8.1":
  version: 0.8.1
  resolution: "@emotion/unitless@npm:0.8.1"
  checksum: 10c0/a1ed508628288f40bfe6dd17d431ed899c067a899fa293a13afe3aed1d70fac0412b8a215fafab0b42829360db687fecd763e5f01a64ddc4a4b58ec3112ff548
  languageName: node
  linkType: hard

"@expo/cli@npm:0.24.13":
  version: 0.24.13
  resolution: "@expo/cli@npm:0.24.13"
  dependencies:
    "@0no-co/graphql.web": "npm:^1.0.8"
    "@babel/runtime": "npm:^7.20.0"
    "@expo/code-signing-certificates": "npm:^0.0.5"
    "@expo/config": "npm:~11.0.10"
    "@expo/config-plugins": "npm:~10.0.2"
    "@expo/devcert": "npm:^1.1.2"
    "@expo/env": "npm:~1.0.5"
    "@expo/image-utils": "npm:^0.7.4"
    "@expo/json-file": "npm:^9.1.4"
    "@expo/metro-config": "npm:~0.20.14"
    "@expo/osascript": "npm:^2.2.4"
    "@expo/package-manager": "npm:^1.8.4"
    "@expo/plist": "npm:^0.3.4"
    "@expo/prebuild-config": "npm:^9.0.6"
    "@expo/spawn-async": "npm:^1.7.2"
    "@expo/ws-tunnel": "npm:^1.0.1"
    "@expo/xcpretty": "npm:^4.3.0"
    "@react-native/dev-middleware": "npm:0.79.2"
    "@urql/core": "npm:^5.0.6"
    "@urql/exchange-retry": "npm:^1.3.0"
    accepts: "npm:^1.3.8"
    arg: "npm:^5.0.2"
    better-opn: "npm:~3.0.2"
    bplist-creator: "npm:0.1.0"
    bplist-parser: "npm:^0.3.1"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.3.0"
    compression: "npm:^1.7.4"
    connect: "npm:^3.7.0"
    debug: "npm:^4.3.4"
    env-editor: "npm:^0.4.1"
    freeport-async: "npm:^2.0.0"
    getenv: "npm:^1.0.0"
    glob: "npm:^10.4.2"
    lan-network: "npm:^0.1.6"
    minimatch: "npm:^9.0.0"
    node-forge: "npm:^1.3.1"
    npm-package-arg: "npm:^11.0.0"
    ora: "npm:^3.4.0"
    picomatch: "npm:^3.0.1"
    pretty-bytes: "npm:^5.6.0"
    pretty-format: "npm:^29.7.0"
    progress: "npm:^2.0.3"
    prompts: "npm:^2.3.2"
    qrcode-terminal: "npm:0.11.0"
    require-from-string: "npm:^2.0.2"
    requireg: "npm:^0.2.2"
    resolve: "npm:^1.22.2"
    resolve-from: "npm:^5.0.0"
    resolve.exports: "npm:^2.0.3"
    semver: "npm:^7.6.0"
    send: "npm:^0.19.0"
    slugify: "npm:^1.3.4"
    source-map-support: "npm:~0.5.21"
    stacktrace-parser: "npm:^0.1.10"
    structured-headers: "npm:^0.4.1"
    tar: "npm:^7.4.3"
    terminal-link: "npm:^2.1.1"
    undici: "npm:^6.18.2"
    wrap-ansi: "npm:^7.0.0"
    ws: "npm:^8.12.1"
  bin:
    expo-internal: build/bin/cli
  checksum: 10c0/1a103c72b778979b932a84a24596a45b9682c32f926fbbbfd2f71c70410da42e1a0f511a92856126fc2fa10d3b833b9c5b8d0e0a784e3eda93f7b9daf594b1b2
  languageName: node
  linkType: hard

"@expo/code-signing-certificates@npm:^0.0.5":
  version: 0.0.5
  resolution: "@expo/code-signing-certificates@npm:0.0.5"
  dependencies:
    node-forge: "npm:^1.2.1"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/98c908c54f92d6782ae01fef47dd858140dc6013e5376ee3faf9b243327f2b16279441fec171cbde45d0e3ebd0bf72db57b4d4c2a0c4f952285b0b377b2b356b
  languageName: node
  linkType: hard

"@expo/config-plugins@npm:~10.0.2":
  version: 10.0.2
  resolution: "@expo/config-plugins@npm:10.0.2"
  dependencies:
    "@expo/config-types": "npm:^53.0.3"
    "@expo/json-file": "npm:~9.1.4"
    "@expo/plist": "npm:^0.3.4"
    "@expo/sdk-runtime-versions": "npm:^1.0.0"
    chalk: "npm:^4.1.2"
    debug: "npm:^4.3.5"
    getenv: "npm:^1.0.0"
    glob: "npm:^10.4.2"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.5.4"
    slash: "npm:^3.0.0"
    slugify: "npm:^1.6.6"
    xcode: "npm:^3.0.1"
    xml2js: "npm:0.6.0"
  checksum: 10c0/4049c2c6dc2589ce0dc840ef971bcdd4215ca74e5057fc92c07004a877c9198dc78bd07b91d3ba908242e70e683faa6fb516f07ec42c2328cbe8e661a65591d0
  languageName: node
  linkType: hard

"@expo/config-types@npm:^53.0.3, @expo/config-types@npm:^53.0.4":
  version: 53.0.4
  resolution: "@expo/config-types@npm:53.0.4"
  checksum: 10c0/e50e584af3bc0cf885333d84b10032aa8a0c3f3b254ed4aaeb3aa3e02dea74ba694f5cb7cf49fe7b7b924cecf337be09e0e5db24cf5802aca51eaae51054f1c4
  languageName: node
  linkType: hard

"@expo/config@npm:~11.0.10, @expo/config@npm:~11.0.9":
  version: 11.0.10
  resolution: "@expo/config@npm:11.0.10"
  dependencies:
    "@babel/code-frame": "npm:~7.10.4"
    "@expo/config-plugins": "npm:~10.0.2"
    "@expo/config-types": "npm:^53.0.4"
    "@expo/json-file": "npm:^9.1.4"
    deepmerge: "npm:^4.3.1"
    getenv: "npm:^1.0.0"
    glob: "npm:^10.4.2"
    require-from-string: "npm:^2.0.2"
    resolve-from: "npm:^5.0.0"
    resolve-workspace-root: "npm:^2.0.0"
    semver: "npm:^7.6.0"
    slugify: "npm:^1.3.4"
    sucrase: "npm:3.35.0"
  checksum: 10c0/dacfc05bf70cc11caf8fd5c4b977cc0eb19512ca5421954672be42fbd4552001003d34da6c2567d494927551f5aceb85b9af36c529113edbcdbcee1ce0ad83fb
  languageName: node
  linkType: hard

"@expo/devcert@npm:^1.1.2":
  version: 1.2.0
  resolution: "@expo/devcert@npm:1.2.0"
  dependencies:
    "@expo/sudo-prompt": "npm:^9.3.1"
    debug: "npm:^3.1.0"
    glob: "npm:^10.4.2"
  checksum: 10c0/3d6a1ce44918c2e5be3bb89d25cfc80551623e4fe5004d4eb29d1edc8edd676258345e64d2aefe56188bc5d4b33e2b7e733a108b2be225af1f90ca86d7170069
  languageName: node
  linkType: hard

"@expo/env@npm:~1.0.5":
  version: 1.0.5
  resolution: "@expo/env@npm:1.0.5"
  dependencies:
    chalk: "npm:^4.0.0"
    debug: "npm:^4.3.4"
    dotenv: "npm:~16.4.5"
    dotenv-expand: "npm:~11.0.6"
    getenv: "npm:^1.0.0"
  checksum: 10c0/6718ed1371d9b9150fe3ffeeefe24e1b7ead585845643dfed07f19270ed2f05d951cceb45962ff4c627359161c1535b448155fc3292d6f0400aebbaa83719830
  languageName: node
  linkType: hard

"@expo/fingerprint@npm:0.12.4":
  version: 0.12.4
  resolution: "@expo/fingerprint@npm:0.12.4"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    arg: "npm:^5.0.2"
    chalk: "npm:^4.1.2"
    debug: "npm:^4.3.4"
    find-up: "npm:^5.0.0"
    getenv: "npm:^1.0.0"
    minimatch: "npm:^9.0.0"
    p-limit: "npm:^3.1.0"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
  bin:
    fingerprint: bin/cli.js
  checksum: 10c0/3cac838023567cafd2e3d53e681b6c00fad887152f31adb2fdeed0eeffcb0ad59c73b17e012b52884a081043b2bcd3250432c517f6ea52fef98df26b0f13474c
  languageName: node
  linkType: hard

"@expo/html-elements@npm:0.4.2":
  version: 0.4.2
  resolution: "@expo/html-elements@npm:0.4.2"
  checksum: 10c0/c83aef547009da9e6c58da9d43048b295ca4010fb0d055bff1aeb6b7cd1def7f2472319b0566d8b56d92f7d1deb2e547328f64c36bd3cbb8dbd118d9cfd9e006
  languageName: node
  linkType: hard

"@expo/image-utils@npm:^0.7.4":
  version: 0.7.4
  resolution: "@expo/image-utils@npm:0.7.4"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.0.0"
    getenv: "npm:^1.0.0"
    jimp-compact: "npm:0.16.1"
    parse-png: "npm:^2.1.0"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
    temp-dir: "npm:~2.0.0"
    unique-string: "npm:~2.0.0"
  checksum: 10c0/4fb1b418ef7f1386d1ad291f285eee16429653ba61e4220ef3aa56b6f86361ff65aed4f4f40803f84c5b85d1326c06be8f2c9762f5ee980f3cb10ceca7b7eb39
  languageName: node
  linkType: hard

"@expo/json-file@npm:^9.1.4, @expo/json-file@npm:~9.1.4":
  version: 9.1.4
  resolution: "@expo/json-file@npm:9.1.4"
  dependencies:
    "@babel/code-frame": "npm:~7.10.4"
    json5: "npm:^2.2.3"
  checksum: 10c0/43c68ba5316dc9b2e79a0a15fbfca60430f64181ae1d47f9627fd1f5b2f27b090a829125168290cbab8af72f0421bab941dbd855217fec4787bde921e6962a05
  languageName: node
  linkType: hard

"@expo/metro-config@npm:0.20.14, @expo/metro-config@npm:~0.20.14":
  version: 0.20.14
  resolution: "@expo/metro-config@npm:0.20.14"
  dependencies:
    "@babel/core": "npm:^7.20.0"
    "@babel/generator": "npm:^7.20.5"
    "@babel/parser": "npm:^7.20.0"
    "@babel/types": "npm:^7.20.0"
    "@expo/config": "npm:~11.0.9"
    "@expo/env": "npm:~1.0.5"
    "@expo/json-file": "npm:~9.1.4"
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.1.0"
    debug: "npm:^4.3.2"
    dotenv: "npm:~16.4.5"
    dotenv-expand: "npm:~11.0.6"
    getenv: "npm:^1.0.0"
    glob: "npm:^10.4.2"
    jsc-safe-url: "npm:^0.2.4"
    lightningcss: "npm:~1.27.0"
    minimatch: "npm:^9.0.0"
    postcss: "npm:~8.4.32"
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/f12729774bc58dc52160b72f0de2837928a930643a3c13e3864f06e76c350a7dc17e00fe1719762d9cf61c61c060651cde5aaaa7ffbffbbdfbbe1cc6820b0575
  languageName: node
  linkType: hard

"@expo/metro-runtime@npm:5.0.4":
  version: 5.0.4
  resolution: "@expo/metro-runtime@npm:5.0.4"
  peerDependencies:
    react-native: "*"
  checksum: 10c0/3522e7e95c13679a4f150d7cce78253e928adea5593c77af6cda0b56c34f2cb4d4a6d057a8bd455b3e3f63439e321c6b80cd1a7bdfdfa04b2ab1fcc034b3736f
  languageName: node
  linkType: hard

"@expo/osascript@npm:^2.2.4":
  version: 2.2.4
  resolution: "@expo/osascript@npm:2.2.4"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    exec-async: "npm:^2.2.0"
  checksum: 10c0/e8e9d6f6f4d78edc083fb50b291b4fd41cd408dfac7e60163555d7f1c3f528c3fd58e7cf2dcb837b697086629ebef40a47fcd3725ca006b8d9db863cc8fbd9b2
  languageName: node
  linkType: hard

"@expo/package-manager@npm:^1.8.4":
  version: 1.8.4
  resolution: "@expo/package-manager@npm:1.8.4"
  dependencies:
    "@expo/json-file": "npm:^9.1.4"
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.0.0"
    npm-package-arg: "npm:^11.0.0"
    ora: "npm:^3.4.0"
    resolve-workspace-root: "npm:^2.0.0"
  checksum: 10c0/b55a68296dcc6f9af668436c8159e164370c34ada1e187866215fadf0e984b27a951e77b2d0ac192dbb759a86c9b31f1603feed6fcefe255af7a93b4291b636d
  languageName: node
  linkType: hard

"@expo/plist@npm:^0.3.4":
  version: 0.3.4
  resolution: "@expo/plist@npm:0.3.4"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.8"
    base64-js: "npm:^1.2.3"
    xmlbuilder: "npm:^15.1.1"
  checksum: 10c0/e382c6ebd998353fecd9508807e51f80f4db48a86457c70e5709436aa772ea9580bc258b6c8ca8930a578b164d87673a6676f47ce0afbe2c9b6bb83d51b9f2b4
  languageName: node
  linkType: hard

"@expo/prebuild-config@npm:^9.0.5, @expo/prebuild-config@npm:^9.0.6":
  version: 9.0.6
  resolution: "@expo/prebuild-config@npm:9.0.6"
  dependencies:
    "@expo/config": "npm:~11.0.9"
    "@expo/config-plugins": "npm:~10.0.2"
    "@expo/config-types": "npm:^53.0.4"
    "@expo/image-utils": "npm:^0.7.4"
    "@expo/json-file": "npm:^9.1.4"
    "@react-native/normalize-colors": "npm:0.79.2"
    debug: "npm:^4.3.1"
    resolve-from: "npm:^5.0.0"
    semver: "npm:^7.6.0"
    xml2js: "npm:0.6.0"
  checksum: 10c0/5be29d038b7195454a0a7e6bd0651422f985937eee51c85450fa9d861119d95030dd47a7130990cc613450b250a741026c1e4b68a068f4873ad0a1a14959d508
  languageName: node
  linkType: hard

"@expo/sdk-runtime-versions@npm:^1.0.0":
  version: 1.0.0
  resolution: "@expo/sdk-runtime-versions@npm:1.0.0"
  checksum: 10c0/f80ae78a294daf396f3eff2eb412948ced5501395a6d3b88058866da9c5135dbacbb2804f8d062222e7452159a61eebefd2f548a2939f539f0f0efe8145588a2
  languageName: node
  linkType: hard

"@expo/server@npm:^0.6.2":
  version: 0.6.2
  resolution: "@expo/server@npm:0.6.2"
  dependencies:
    abort-controller: "npm:^3.0.0"
    debug: "npm:^4.3.4"
    source-map-support: "npm:~0.5.21"
    undici: "npm:^6.18.2 || ^7.0.0"
  checksum: 10c0/ef2f22170ce47721293dafb001fc947d1725c9f79c36b8eb749227ada0bab3eacd1e8ca9bf59a724f8754c0bb5256b949c8e899720edcb4e1a84528f69573126
  languageName: node
  linkType: hard

"@expo/spawn-async@npm:^1.7.2":
  version: 1.7.2
  resolution: "@expo/spawn-async@npm:1.7.2"
  dependencies:
    cross-spawn: "npm:^7.0.3"
  checksum: 10c0/0548c4e95ee39393c2f3919bc605f21eba4f0a8ba66fa82fbbc4b1b624e0054526918489227b924f03af5bc156a011f39a2472c223c0d2237fb7afd8dedd5357
  languageName: node
  linkType: hard

"@expo/sudo-prompt@npm:^9.3.1":
  version: 9.3.2
  resolution: "@expo/sudo-prompt@npm:9.3.2"
  checksum: 10c0/032652bf1c3f326c9c194f336de5821b9ece9d48b22e3e277950d939fcd728c85459680a9771705904d375f128221cca2e1e91c5d7a85cf3c07fe6f88c361e9d
  languageName: node
  linkType: hard

"@expo/vector-icons@npm:^14.0.0, @expo/vector-icons@npm:^14.0.2":
  version: 14.1.0
  resolution: "@expo/vector-icons@npm:14.1.0"
  peerDependencies:
    expo-font: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/f1dcea2c43c0808f48d1953395c6f8025ae5e811648e86b79158492c9ef8af7a40781e42844dfb1434242a08fcf6ab14886825eb2c79bad2a792aebd1eb5077c
  languageName: node
  linkType: hard

"@expo/ws-tunnel@npm:^1.0.1":
  version: 1.0.6
  resolution: "@expo/ws-tunnel@npm:1.0.6"
  checksum: 10c0/050eb7fbd54b636c97c818e7ec5402ce616cae655290386a51600b200947e281cdd12d182251c07fab449e11a732135d61429b738cd03945e94757061e652ecd
  languageName: node
  linkType: hard

"@expo/xcpretty@npm:^4.3.0":
  version: 4.3.2
  resolution: "@expo/xcpretty@npm:4.3.2"
  dependencies:
    "@babel/code-frame": "npm:7.10.4"
    chalk: "npm:^4.1.0"
    find-up: "npm:^5.0.0"
    js-yaml: "npm:^4.1.0"
  bin:
    excpretty: build/cli.js
  checksum: 10c0/e524817b2e42fb8c8914fca7e8f7c2f723f4f6d338a57b7ae97cd3e76da8108af63a22d4c7dc2e96a192a248a242f6e0f8056f0ca53bc4fb5cd2e5ae428e0891
  languageName: node
  linkType: hard

"@formatjs/ecma402-abstract@npm:2.3.4":
  version: 2.3.4
  resolution: "@formatjs/ecma402-abstract@npm:2.3.4"
  dependencies:
    "@formatjs/fast-memoize": "npm:2.2.7"
    "@formatjs/intl-localematcher": "npm:0.6.1"
    decimal.js: "npm:^10.4.3"
    tslib: "npm:^2.8.0"
  checksum: 10c0/2644bc618a34dc610ef9691281eeb45ae6175e6982cf19f1bd140672fc95c748747ce3c85b934649ea7e4a304f7ae0060625fd53d5df76f92ca3acf743e1eb0a
  languageName: node
  linkType: hard

"@formatjs/fast-memoize@npm:2.2.7":
  version: 2.2.7
  resolution: "@formatjs/fast-memoize@npm:2.2.7"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10c0/f5eabb0e4ab7162297df8252b4cfde194b23248120d9df267592eae2be2d2f7c4f670b5a70523d91b4ecdc35d40e65823bb8eeba8dd79fbf8601a972bf3b8866
  languageName: node
  linkType: hard

"@formatjs/icu-messageformat-parser@npm:2.11.2":
  version: 2.11.2
  resolution: "@formatjs/icu-messageformat-parser@npm:2.11.2"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:2.3.4"
    "@formatjs/icu-skeleton-parser": "npm:1.8.14"
    tslib: "npm:^2.8.0"
  checksum: 10c0/a121f2d2c6b36a1632ffd64c3545e2500c8ee0f7fee5db090318c035d635c430ab123faedb5d000f18d9423a7b55fbf670b84e2e2dd72cc307a38aed61d3b2e0
  languageName: node
  linkType: hard

"@formatjs/icu-skeleton-parser@npm:1.8.14":
  version: 1.8.14
  resolution: "@formatjs/icu-skeleton-parser@npm:1.8.14"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:2.3.4"
    tslib: "npm:^2.8.0"
  checksum: 10c0/a1807ed6e90b8a2e8d0e5b5125e6f9a2c057d3cff377fb031d2333af7cfaa6de4ed3a15c23da7294d4c3557f8b28b2163246434a19720f26b5db0497d97e9b58
  languageName: node
  linkType: hard

"@formatjs/intl-localematcher@npm:0.6.1":
  version: 0.6.1
  resolution: "@formatjs/intl-localematcher@npm:0.6.1"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10c0/bacbedd508519c1bb5ca2620e89dc38f12101be59439aa14aa472b222915b462cb7d679726640f6dcf52a05dd218b5aa27ccd60f2e5010bb96f1d4929848cde0
  languageName: node
  linkType: hard

"@gluestack-ui/actionsheet@npm:^0.2.52":
  version: 0.2.53
  resolution: "@gluestack-ui/actionsheet@npm:0.2.53"
  dependencies:
    "@gluestack-ui/hooks": "npm:0.1.13"
    "@gluestack-ui/overlay": "npm:^0.1.22"
    "@gluestack-ui/transitions": "npm:^0.1.11"
    "@gluestack-ui/utils": "npm:^0.1.15"
    "@react-native-aria/dialog": "npm:^0.0.5"
    "@react-native-aria/focus": "npm:^0.2.9"
    "@react-native-aria/interactions": "npm:0.2.16"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/9e0977cb62df7ddc26b11ada1f8fa7d16b398f4b7355be0dee38ff78d56a3e5247cbffda9618e21c518aefceb9b07091b6383307b8aa132c0f5d670851e3ecdd
  languageName: node
  linkType: hard

"@gluestack-ui/alert-dialog@npm:^0.1.38":
  version: 0.1.38
  resolution: "@gluestack-ui/alert-dialog@npm:0.1.38"
  dependencies:
    "@gluestack-ui/hooks": "npm:0.1.13"
    "@gluestack-ui/overlay": "npm:^0.1.22"
    "@gluestack-ui/utils": "npm:^0.1.15"
    "@react-native-aria/dialog": "npm:^0.0.5"
    "@react-native-aria/focus": "npm:^0.2.9"
    "@react-native-aria/interactions": "npm:0.2.16"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/812f76ac11b9ec62b17a94989dbc981a1b1e944c520313983df323dadc2d08596ecfa654de90c7186363dfb6583429f84231e1f81ef8c25a3cff26f9905f2c00
  languageName: node
  linkType: hard

"@gluestack-ui/hooks@npm:0.1.13":
  version: 0.1.13
  resolution: "@gluestack-ui/hooks@npm:0.1.13"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/dcdb65923ae5c26cf386ea2825d589f79915cfacdbf00ce443e845c8f51dbba3c1adec73ef90c84d3ead4e7cb4af34652205ace27dbe110d330ce9fdd1086e83
  languageName: node
  linkType: hard

"@gluestack-ui/icon@npm:^0.1.26":
  version: 0.1.27
  resolution: "@gluestack-ui/icon@npm:0.1.27"
  dependencies:
    "@gluestack-ui/provider": "npm:^0.1.19"
    "@gluestack-ui/utils": "npm:^0.1.14"
    "@react-native-aria/focus": "npm:^0.2.9"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/c5024819d548fa79847ece23c4d2059de799334356d4c88d0fc8b3b96fac4efdcac1a589e5a4b615d98ec12439fe39ab8e61adae05f7e46adc60038a1692d7f9
  languageName: node
  linkType: hard

"@gluestack-ui/nativewind-utils@npm:^1.0.26":
  version: 1.0.26
  resolution: "@gluestack-ui/nativewind-utils@npm:1.0.26"
  dependencies:
    find-yarn-workspace-root: "npm:^2.0.0"
    patch-package: "npm:8.0.0"
    tailwind-variants: "npm:0.1.20"
  peerDependencies:
    nativewind: ">=4.0"
    react: ">=16"
  checksum: 10c0/d3e24b916f92d0679bfeb839238579a52ea679d951dcbcbeae388ecae6032a0fba3a8f944c784230b76f7917762be0d9931c040e8196f6128e30fba223a3342c
  languageName: node
  linkType: hard

"@gluestack-ui/overlay@npm:^0.1.16, @gluestack-ui/overlay@npm:^0.1.20, @gluestack-ui/overlay@npm:^0.1.22":
  version: 0.1.22
  resolution: "@gluestack-ui/overlay@npm:0.1.22"
  dependencies:
    "@react-native-aria/focus": "npm:^0.2.9"
    "@react-native-aria/interactions": "npm:0.2.16"
    "@react-native-aria/overlays": "npm:^0.3.15"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/a2c80d395de37250729e2510c51a4d7d7b310b49fd197de6a2ac284d0acbd966df80a10a17167317f3c7e31da33a3813c0330758f19e89b9f5ad1b7d1f059845
  languageName: node
  linkType: hard

"@gluestack-ui/provider@npm:^0.1.19":
  version: 0.1.19
  resolution: "@gluestack-ui/provider@npm:0.1.19"
  dependencies:
    "@react-native-aria/interactions": "npm:0.2.16"
    tsconfig: "npm:7"
    typescript: "npm:^5.6.3"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/d0faf389563c4c965bfd2d994ae5f54570c9df181e80f5b18298de9d4af11956ac7905456a5a5fdc6feb0c7abe27bbb553a976c9807b2691e4d12290e74cdb92
  languageName: node
  linkType: hard

"@gluestack-ui/react-native-aria@npm:^0.1.6":
  version: 0.1.7
  resolution: "@gluestack-ui/react-native-aria@npm:0.1.7"
  dependencies:
    "@react-native-aria/focus": "npm:^0.2.9"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/7ddc4492994fea1974e524cb8bb663253a205fe8b6ddde8725f6230132b67838ad8a8a5f5edb2a9d1c381d9d921532687ead5a3f05daf08f8090d1aba996d35a
  languageName: node
  linkType: hard

"@gluestack-ui/toast@npm:^1.0.9":
  version: 1.0.9
  resolution: "@gluestack-ui/toast@npm:1.0.9"
  dependencies:
    "@gluestack-ui/hooks": "npm:0.1.13"
    "@gluestack-ui/overlay": "npm:^0.1.20"
    "@gluestack-ui/transitions": "npm:^0.1.11"
    "@gluestack-ui/utils": "npm:^0.1.14"
    "@react-native-aria/focus": "npm:^0.2.9"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/6487b134dcdd68f617120b4a91e71cbd7c08d01b0a668c0f25910d73e489e2419dd48a50d09e11aeb375440b89408b11a33872657ea127d8bc02b97d24f483e4
  languageName: node
  linkType: hard

"@gluestack-ui/transitions@npm:^0.1.11":
  version: 0.1.11
  resolution: "@gluestack-ui/transitions@npm:0.1.11"
  dependencies:
    "@gluestack-ui/overlay": "npm:^0.1.16"
    "@gluestack-ui/react-native-aria": "npm:^0.1.6"
    "@gluestack-ui/utils": "npm:^0.1.14"
    "@react-native-aria/focus": "npm:^0.2.9"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/5af71e8a974c3e09098b9fc615b972bd3effe3aac14fea41cbcbdd89ea6a7e54ce92baa325e4d5c6f7b232a81440e80098b3f2cf89d389d0108d71755893890d
  languageName: node
  linkType: hard

"@gluestack-ui/utils@npm:^0.1.14, @gluestack-ui/utils@npm:^0.1.15":
  version: 0.1.15
  resolution: "@gluestack-ui/utils@npm:0.1.15"
  dependencies:
    "@react-native-aria/focus": "npm:^0.2.9"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 10c0/4181d431aa8647ea1cc4982a9f2370a3333e0c8cecddf1f0e5134396fd32da04da6a8cdf4680bce7839b6673f121ec3c0d9bb31e2b4cb37fa85ba9e80a16ce58
  languageName: node
  linkType: hard

"@gorhom/bottom-sheet@npm:^5":
  version: 5.1.4
  resolution: "@gorhom/bottom-sheet@npm:5.1.4"
  dependencies:
    "@gorhom/portal": "npm:1.0.14"
    invariant: "npm:^2.2.4"
  peerDependencies:
    "@types/react": "*"
    "@types/react-native": "*"
    react: "*"
    react-native: "*"
    react-native-gesture-handler: ">=2.16.1"
    react-native-reanimated: ">=3.16.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-native":
      optional: true
  checksum: 10c0/1ef4003de33ebe7d3ae2c76514ce9af9011c0792be2f4387cb820a30b027c589962fede1b08936785b1cb16dfb5e614c3f835a69e6f7a61f3e78f38ab61e4eec
  languageName: node
  linkType: hard

"@gorhom/portal@npm:1.0.14":
  version: 1.0.14
  resolution: "@gorhom/portal@npm:1.0.14"
  dependencies:
    nanoid: "npm:^3.3.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/86f33afc2ac2656a86a6f3fd1e41565419839576ede2c38333434a93a0a2fe4fb6fc18ab3360579427f2a1fc3b4564b933cc5ae1793a7e2825c93860a00b215f
  languageName: node
  linkType: hard

"@hookform/resolvers@npm:^3.10.0":
  version: 3.10.0
  resolution: "@hookform/resolvers@npm:3.10.0"
  peerDependencies:
    react-hook-form: ^7.0.0
  checksum: 10c0/7ee44533b4cdc28c4fa2a94894c735411e5a1f830f4a617c580533321a9b901df0cc8c1e2fad81ad8d55154ebc5cb844cf9c116a3148ffae2bc48758c33cbb8e
  languageName: node
  linkType: hard

"@internationalized/date@npm:^3.8.0":
  version: 3.8.0
  resolution: "@internationalized/date@npm:3.8.0"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/7ac0cae2f1832fe2f2950e22208812ed8bf2845dd903ec93bd3aa024ca020124e137638b11bb5817b92abde1daa3f881cc81d62db0b20f5db2d9e07ab0cd9e01
  languageName: node
  linkType: hard

"@internationalized/message@npm:^3.1.7":
  version: 3.1.7
  resolution: "@internationalized/message@npm:3.1.7"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
    intl-messageformat: "npm:^10.1.0"
  checksum: 10c0/0e3d46c97e790e34074f2589cbbe220bac8da453bf6d5d5da5d545b8a3989d37dc02d5209296f3cb900cea5a1220658821c7fe04fd00b2a27c446fcc6f062b1a
  languageName: node
  linkType: hard

"@internationalized/number@npm:^3.6.1":
  version: 3.6.1
  resolution: "@internationalized/number@npm:3.6.1"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/2605245bec05c5ff2e100d0cf5541b1f08e9038d76c18f11ce9142f538a5a06f1f5459506e7ef6d1b162b4bcc34bf2d8eebb39fe235048cb41eebd00ec04fd65
  languageName: node
  linkType: hard

"@internationalized/string@npm:^3.2.6":
  version: 3.2.6
  resolution: "@internationalized/string@npm:3.2.6"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/8ed556697fee4aa2a115ea9d44075e8be8a7f80c76ebfcc6a4f14681175c4e59438f7ba049748d9c9cd0b46c7927b731d7c1f7fa53aaaf58b4c46dbd9f471b61
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@isaacs/ttlcache@npm:^1.4.1":
  version: 1.4.1
  resolution: "@isaacs/ttlcache@npm:1.4.1"
  checksum: 10c0/6921de516917b02673a58e543c2b06fd04237cbf6d089ca22d6e98defa4b1e9a48258cb071d6b581284bb497bea687320788830541511297eecbe6e93a665bbf
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: "npm:^5.3.1"
    find-up: "npm:^4.1.0"
    get-package-type: "npm:^0.1.0"
    js-yaml: "npm:^3.13.1"
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/dd2a8b094887da5a1a2339543a4933d06db2e63cbbc2e288eb6431bd832065df0c099d091b6a67436e71b7d6bf85f01ce7c15f9253b4cbebcc3b9a496165ba42
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10c0/61c5286771676c9ca3eb2bd8a7310a9c063fb6e0e9712225c8471c582d157392c88f5353581c8c9adbe0dff98892317d2fdfc56c3499aa42e0194405206a963a
  languageName: node
  linkType: hard

"@jest/console@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/console@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/7be408781d0a6f657e969cbec13b540c329671819c2f57acfad0dae9dbfe2c9be859f38fe99b35dba9ff1536937dc6ddc69fdcd2794812fa3c647a1619797f6c
  languageName: node
  linkType: hard

"@jest/core@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/core@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/reporters": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    exit: "npm:^0.1.2"
    graceful-fs: "npm:^4.2.9"
    jest-changed-files: "npm:^29.7.0"
    jest-config: "npm:^29.7.0"
    jest-haste-map: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-resolve-dependencies: "npm:^29.7.0"
    jest-runner: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    jest-watcher: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10c0/934f7bf73190f029ac0f96662c85cd276ec460d407baf6b0dbaec2872e157db4d55a7ee0b1c43b18874602f662b37cb973dda469a4e6d88b4e4845b521adeeb2
  languageName: node
  linkType: hard

"@jest/create-cache-key-function@npm:^29.2.1, @jest/create-cache-key-function@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/create-cache-key-function@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
  checksum: 10c0/5c47ef62205264adf77b1ff26b969ce9fe84920b8275c3c5e83f4236859d6ae5e4e7027af99eef04a8e334c4e424d44af3e167972083406070aca733ac2a2795
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/environment@npm:29.7.0"
  dependencies:
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
  checksum: 10c0/c7b1b40c618f8baf4d00609022d2afa086d9c6acc706f303a70bb4b67275868f620ad2e1a9efc5edd418906157337cce50589a627a6400bbdf117d351b91ef86
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect-utils@npm:29.7.0"
  dependencies:
    jest-get-type: "npm:^29.6.3"
  checksum: 10c0/60b79d23a5358dc50d9510d726443316253ecda3a7fb8072e1526b3e0d3b14f066ee112db95699b7a43ad3f0b61b750c72e28a5a1cac361d7a2bb34747fa938a
  languageName: node
  linkType: hard

"@jest/expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect@npm:29.7.0"
  dependencies:
    expect: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
  checksum: 10c0/b41f193fb697d3ced134349250aed6ccea075e48c4f803159db102b826a4e473397c68c31118259868fd69a5cba70e97e1c26d2c2ff716ca39dc73a2ccec037e
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/fake-timers@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@sinonjs/fake-timers": "npm:^10.0.2"
    "@types/node": "npm:*"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/cf0a8bcda801b28dc2e2b2ba36302200ee8104a45ad7a21e6c234148932f826cb3bc57c8df3b7b815aeea0861d7b6ca6f0d4778f93b9219398ef28749e03595c
  languageName: node
  linkType: hard

"@jest/globals@npm:^29.2.1, @jest/globals@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/globals@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/expect": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    jest-mock: "npm:^29.7.0"
  checksum: 10c0/a385c99396878fe6e4460c43bd7bb0a5cc52befb462cc6e7f2a3810f9e7bcce7cdeb51908fd530391ee452dc856c98baa2c5f5fa8a5b30b071d31ef7f6955cea
  languageName: node
  linkType: hard

"@jest/reporters@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/reporters@npm:29.7.0"
  dependencies:
    "@bcoe/v8-coverage": "npm:^0.2.3"
    "@jest/console": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    collect-v8-coverage: "npm:^1.0.0"
    exit: "npm:^0.1.2"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    istanbul-lib-coverage: "npm:^3.0.0"
    istanbul-lib-instrument: "npm:^6.0.0"
    istanbul-lib-report: "npm:^3.0.0"
    istanbul-lib-source-maps: "npm:^4.0.0"
    istanbul-reports: "npm:^3.1.3"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    string-length: "npm:^4.0.1"
    strip-ansi: "npm:^6.0.0"
    v8-to-istanbul: "npm:^9.0.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10c0/a754402a799541c6e5aff2c8160562525e2a47e7d568f01ebfc4da66522de39cbb809bbb0a841c7052e4270d79214e70aec3c169e4eae42a03bc1a8a20cb9fa2
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/schemas@npm:29.6.3"
  dependencies:
    "@sinclair/typebox": "npm:^0.27.8"
  checksum: 10c0/b329e89cd5f20b9278ae1233df74016ebf7b385e0d14b9f4c1ad18d096c4c19d1e687aa113a9c976b16ec07f021ae53dea811fb8c1248a50ac34fbe009fdf6be
  languageName: node
  linkType: hard

"@jest/source-map@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/source-map@npm:29.6.3"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    callsites: "npm:^3.0.0"
    graceful-fs: "npm:^4.2.9"
  checksum: 10c0/a2f177081830a2e8ad3f2e29e20b63bd40bade294880b595acf2fc09ec74b6a9dd98f126a2baa2bf4941acd89b13a4ade5351b3885c224107083a0059b60a219
  languageName: node
  linkType: hard

"@jest/test-result@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-result@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    collect-v8-coverage: "npm:^1.0.0"
  checksum: 10c0/7de54090e54a674ca173470b55dc1afdee994f2d70d185c80236003efd3fa2b753fff51ffcdda8e2890244c411fd2267529d42c4a50a8303755041ee493e6a04
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-sequencer@npm:29.7.0"
  dependencies:
    "@jest/test-result": "npm:^29.7.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/593a8c4272797bb5628984486080cbf57aed09c7cfdc0a634e8c06c38c6bef329c46c0016e84555ee55d1cd1f381518cf1890990ff845524c1123720c8c1481b
  languageName: node
  linkType: hard

"@jest/transform@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/transform@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    babel-plugin-istanbul: "npm:^6.1.1"
    chalk: "npm:^4.0.0"
    convert-source-map: "npm:^2.0.0"
    fast-json-stable-stringify: "npm:^2.1.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pirates: "npm:^4.0.4"
    slash: "npm:^3.0.0"
    write-file-atomic: "npm:^4.0.2"
  checksum: 10c0/7f4a7f73dcf45dfdf280c7aa283cbac7b6e5a904813c3a93ead7e55873761fc20d5c4f0191d2019004fac6f55f061c82eb3249c2901164ad80e362e7a7ede5a6
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/types@npm:29.6.3"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^3.0.0"
    "@types/node": "npm:*"
    "@types/yargs": "npm:^17.0.8"
    chalk: "npm:^4.0.0"
  checksum: 10c0/ea4e493dd3fb47933b8ccab201ae573dcc451f951dc44ed2a86123cd8541b82aa9d2b1031caf9b1080d6673c517e2dcc25a44b2dc4f3fbc37bfc965d444888c0
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.2, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10c0/6a4ecc713ed246ff8e5bdcc1ef7c49aaa93f7463d948ba5054dda18b02dcc6a055e2828c577bcceee058f302ce1fc95595713d44f5c45e43d459f88d267f2f04
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.18, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@legendapp/motion@npm:^2.4.0":
  version: 2.4.0
  resolution: "@legendapp/motion@npm:2.4.0"
  dependencies:
    "@legendapp/tools": "npm:2.0.1"
  peerDependencies:
    nativewind: "*"
    react: ">=16"
    react-native: "*"
  checksum: 10c0/db159d76a627373f7a0bf4e63dab13bdbcf5db39621f603a9d81687b98bf26c5787d318af18f90ed4d5f4a0f7ffd7e9d965fd42d5be2525e5d436c9421fe2843
  languageName: node
  linkType: hard

"@legendapp/tools@npm:2.0.1":
  version: 2.0.1
  resolution: "@legendapp/tools@npm:2.0.1"
  peerDependencies:
    react: ">=16"
  peerDependenciesMeta:
    react:
      optional: true
  checksum: 10c0/98e1a898dd9f1ed597a7bdc228c29fb6bde0224b396ba7e0ea031ea3e4b7e08be81e8da055a46bf0229e6eafc97537ff3ad9c9ce0a65077ca33841a2ab1fcf60
  languageName: node
  linkType: hard

"@meteorrn/core@npm:^2.8.1, @meteorrn/core@npm:^2.8.1-rc.0":
  version: 2.8.1
  resolution: "@meteorrn/core@npm:2.8.1"
  dependencies:
    "@meteorrn/core": "npm:^2.8.1-rc.0"
    "@meteorrn/minimongo": "npm:1.0.1"
    "@react-native-async-storage/async-storage": "npm:>=1.13.0"
    ejson: "npm:2.2.3"
    eventemitter3: "npm:^5.0.1"
  peerDependencies:
    "@react-native-community/netinfo": "*"
    react: "*"
    react-native: "*"
  dependenciesMeta:
    "@react-native-async-storage/async-storage":
      optional: true
  checksum: 10c0/fc28dfb9f047d9c4d5ee9cdff975d6bf5acbbdc0c3681ab5eb005401b022abd7367416760ca8a3946a98f077df8baf80e3e8c585f9438cbc3ba3a50190dc256d
  languageName: node
  linkType: hard

"@meteorrn/minimongo@npm:1.0.1":
  version: 1.0.1
  resolution: "@meteorrn/minimongo@npm:1.0.1"
  dependencies:
    base64-js: "npm:^1.5.1"
    eventemitter3: "npm:^1.1.0"
    invariant: "npm:^2.1.1"
  checksum: 10c0/89818b387897fdd4758306e42958b1845e64bc85c2c2ed328409a7c651dd757618bbd7e059d865ef787410ff5e70d00b3ceb82e8ff4ab977a8073aaaa5b74f95
  languageName: node
  linkType: hard

"@mongodb-js/saslprep@npm:^1.1.0":
  version: 1.2.2
  resolution: "@mongodb-js/saslprep@npm:1.2.2"
  dependencies:
    sparse-bitfield: "npm:^3.0.3"
  checksum: 10c0/6ea882034fe99c6abe033d843dfa0f90fcec105ef538c04ee32aca9d2cda7cc8f2dd45c94c2dbf30f14668524ed076f7072bce857f9ee321ad5b0bc1f930c423
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@radix-ui/react-compose-refs@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-compose-refs@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/d36a9c589eb75d634b9b139c80f916aadaf8a68a7c1c4b8c6c6b88755af1a92f2e343457042089f04cc3f23073619d08bb65419ced1402e9d4e299576d970771
  languageName: node
  linkType: hard

"@radix-ui/react-slot@npm:1.2.0":
  version: 1.2.0
  resolution: "@radix-ui/react-slot@npm:1.2.0"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/f1455f36479e87a0a2254fc2e2b2aba6740d1fbcada949071210bf2a009a031ad508ac01b544bce96337bcca82f49531b46c71615141a5985aaa11ae69b967b1
  languageName: node
  linkType: hard

"@react-aria/dialog@npm:*":
  version: 3.5.24
  resolution: "@react-aria/dialog@npm:3.5.24"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/overlays": "npm:^3.27.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/dialog": "npm:^3.5.17"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/23d14b9558ecc01f6eb5845de3844d4fd5e48e164e6f62b7701cd2b3ec8a9ea3e7c36d079d03c6929230d877847e69563f9b9756f89c6240aa6f7d472dd30a35
  languageName: node
  linkType: hard

"@react-aria/focus@npm:^3.2.3, @react-aria/focus@npm:^3.20.2":
  version: 3.20.2
  resolution: "@react-aria/focus@npm:3.20.2"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
    clsx: "npm:^2.0.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/83c7ce227affed990833664b75c99601390ea9c879a44032541447268da22508712c512f5a943f702aef07bfe1e0ea51f554f49db132f17d80b2da9cb71ec687
  languageName: node
  linkType: hard

"@react-aria/i18n@npm:^3.12.8":
  version: 3.12.8
  resolution: "@react-aria/i18n@npm:3.12.8"
  dependencies:
    "@internationalized/date": "npm:^3.8.0"
    "@internationalized/message": "npm:^3.1.7"
    "@internationalized/number": "npm:^3.6.1"
    "@internationalized/string": "npm:^3.2.6"
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/fc6ccd3a44b084a9ad4001f8adfd086598ed4be229b51c3702b3d289003a67cf0ffd1d93174c16e51bb1745930571ba277d3132a25e6f8d4cc610ef273da7bdb
  languageName: node
  linkType: hard

"@react-aria/interactions@npm:^3.25.0, @react-aria/interactions@npm:^3.3.2":
  version: 3.25.0
  resolution: "@react-aria/interactions@npm:3.25.0"
  dependencies:
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/flags": "npm:^3.1.1"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/091c7b7b2f94b2fefed440b97a986eff0320d5ba5e1a43c195a912e035aa8ce4d4be15f1852b2dffdbcc6a70190a74f0809e4d4cf3d7646b70c9f9b0fca829f5
  languageName: node
  linkType: hard

"@react-aria/overlays@npm:^3.27.0, @react-aria/overlays@npm:^3.7.0":
  version: 3.27.0
  resolution: "@react-aria/overlays@npm:3.27.0"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-aria/visually-hidden": "npm:^3.8.22"
    "@react-stately/overlays": "npm:^3.6.15"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/overlays": "npm:^3.8.14"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/2835dba32a055160341b0c04c8922c4cfb905d2c630508f9b6fc94844131e5359fd27e72f44b7d313339519285465cc6b08c720f2deb8511185d1bdd6c7b10f3
  languageName: node
  linkType: hard

"@react-aria/ssr@npm:^3.0.1, @react-aria/ssr@npm:^3.9.8":
  version: 3.9.8
  resolution: "@react-aria/ssr@npm:3.9.8"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/848cac34f8584477ab6c91686ab447c7f7eee997e0b1771cc71298d15a4dd0400ce7b899ad8c1603a72d59a72f24a390964133693a3ba602828801d4dacc3f45
  languageName: node
  linkType: hard

"@react-aria/utils@npm:^3.28.2, @react-aria/utils@npm:^3.3.0, @react-aria/utils@npm:^3.6.0":
  version: 3.28.2
  resolution: "@react-aria/utils@npm:3.28.2"
  dependencies:
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-stately/flags": "npm:^3.1.1"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
    clsx: "npm:^2.0.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/69fc00d5dbd0fae9349a06cc3bcf327aa1edcb9049a491d0949b4de30c1b7669edc7150cc6885aa362af74a21b68c400b2965c3e117871264c47c379f4a98695
  languageName: node
  linkType: hard

"@react-aria/visually-hidden@npm:^3.8.22":
  version: 3.8.22
  resolution: "@react-aria/visually-hidden@npm:3.8.22"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/fbf527b526250865731737d3d421d796ab0a2a2bb0dbd2532a506693fe8902fa2a53b0afbb7ee4d295028621bdcbe8e50583d3d6d0f87d5ae014eb97953e0a2a
  languageName: node
  linkType: hard

"@react-native-aria/dialog@npm:^0.0.5":
  version: 0.0.5
  resolution: "@react-native-aria/dialog@npm:0.0.5"
  dependencies:
    "@react-aria/dialog": "npm:*"
    "@react-native-aria/utils": "npm:0.2.12"
    "@react-types/dialog": "npm:*"
    "@react-types/shared": "npm:*"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/3ca0a6a207621ce62a1045dd32334b5ddfea3128ae235faf4852913e20ed143ec69bdb22f2b8ff197da3349f812b2e22fa2fa7c9af5d18fbe45740145206279a
  languageName: node
  linkType: hard

"@react-native-aria/focus@npm:^0.2.9":
  version: 0.2.9
  resolution: "@react-native-aria/focus@npm:0.2.9"
  dependencies:
    "@react-aria/focus": "npm:^3.2.3"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/1bbfdcfd584ac9bad44ca4c128501e0727a6616012747cfff344f2230a7654a75c099cca8561fa558c897e25e077ec1acd6d4b4f1eed5bfcd5cb972fd350e7a7
  languageName: node
  linkType: hard

"@react-native-aria/interactions@npm:0.2.16":
  version: 0.2.16
  resolution: "@react-native-aria/interactions@npm:0.2.16"
  dependencies:
    "@react-aria/interactions": "npm:^3.3.2"
    "@react-aria/utils": "npm:^3.6.0"
    "@react-native-aria/utils": "npm:0.2.12"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/68c7d809ff41331821f2d828dab8f2af196b609b0f470ef703c99f297f726582db9f6dc1975abeab7825a3acbf57c97489a0c896138d5d114214fa9a346d66c8
  languageName: node
  linkType: hard

"@react-native-aria/overlays@npm:^0.3.15":
  version: 0.3.15
  resolution: "@react-native-aria/overlays@npm:0.3.15"
  dependencies:
    "@react-aria/interactions": "npm:^3.3.2"
    "@react-aria/overlays": "npm:^3.7.0"
    "@react-native-aria/utils": "npm:0.2.12"
    "@react-stately/overlays": "npm:^3.1.1"
    "@react-types/overlays": "npm:^3.4.0"
    dom-helpers: "npm:^5.0.0"
  peerDependencies:
    react: "*"
    react-dom: "*"
    react-native: "*"
  checksum: 10c0/e2eae18ae0bf003ffc49bb756907a3b3ef44f378c7b47b51d98a009c95f84e313952ae69b7010f0bbcead55cb80a21a84919e02069c20ced3e10513004f23b27
  languageName: node
  linkType: hard

"@react-native-aria/utils@npm:0.2.12":
  version: 0.2.12
  resolution: "@react-native-aria/utils@npm:0.2.12"
  dependencies:
    "@react-aria/ssr": "npm:^3.0.1"
    "@react-aria/utils": "npm:^3.3.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/f888dd56713a35f2d740023bce5dfc82eacf1209ef52e9048a7b2fd66dee9e44ced316551f5061573802693c1f3a16781388e1d7eb4224b3409e64f950415d80
  languageName: node
  linkType: hard

"@react-native-async-storage/async-storage@npm:2.1.2, @react-native-async-storage/async-storage@npm:>=1.13.0":
  version: 2.1.2
  resolution: "@react-native-async-storage/async-storage@npm:2.1.2"
  dependencies:
    merge-options: "npm:^3.0.4"
  peerDependencies:
    react-native: ^0.0.0-0 || >=0.65 <1.0
  checksum: 10c0/8f3d6ff1b32ef8705c5c8be8248988cfbfd571c0e8142b8aef15429f13ddc9a018792b4be837215f6592c76b9cd99a931d4f0ab4182eebd8bddede458d484053
  languageName: node
  linkType: hard

"@react-native-community/datetimepicker@npm:8.3.0":
  version: 8.3.0
  resolution: "@react-native-community/datetimepicker@npm:8.3.0"
  dependencies:
    invariant: "npm:^2.2.4"
  peerDependencies:
    expo: ">=50.0.0"
    react: "*"
    react-native: "*"
    react-native-windows: "*"
  peerDependenciesMeta:
    expo:
      optional: true
    react-native-windows:
      optional: true
  checksum: 10c0/33918386270dafd8240dca76619fcc8ad4903485d55564aad19f1cd7fed28ca2bf669aadb686d139f4700dd8571ba861d19f77b3a748a7f9ce8c152476593f97
  languageName: node
  linkType: hard

"@react-native-community/netinfo@npm:^11.4.1":
  version: 11.4.1
  resolution: "@react-native-community/netinfo@npm:11.4.1"
  peerDependencies:
    react-native: ">=0.59"
  checksum: 10c0/118ba6d495bd14c2dab3d228a0c1857620a293ae8592d170d70eaf88a6ac6a4e35c7f84527975a9054e36891a9694aacc8ec053326b5f05961459ed08a70dd35
  languageName: node
  linkType: hard

"@react-native-google-signin/google-signin@npm:^13.1.0":
  version: 13.3.1
  resolution: "@react-native-google-signin/google-signin@npm:13.3.1"
  peerDependencies:
    expo: ">=50.0.0"
    react: "*"
    react-dom: "*"
    react-native: "*"
  peerDependenciesMeta:
    expo:
      optional: true
    react-dom:
      optional: true
  checksum: 10c0/39be6c3bb52be30a8b6c49376ea90c325c07f63c01938df9ba4b3d8966f69c7e1e8fe8ab000227939b899fce06efd3725f3fba56848cf9d25eb9507f8f994f8c
  languageName: node
  linkType: hard

"@react-native/assets-registry@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/assets-registry@npm:0.79.2"
  checksum: 10c0/a3ce4ebf293924366cb1005253bd012f0c16c46a004b96a94d9a495d311a845701c90471d9389f28efc991633a4d02cee029a30720f80fe2e470f803a164a37d
  languageName: node
  linkType: hard

"@react-native/babel-plugin-codegen@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/babel-plugin-codegen@npm:0.79.2"
  dependencies:
    "@babel/traverse": "npm:^7.25.3"
    "@react-native/codegen": "npm:0.79.2"
  checksum: 10c0/5a5ab4c55402a59ccb8768e3aef46b134e1639c78d90cd59967a646a346fcb14b310ff5540abaea942eeebb55cdff141ef42f9762d28e49a526748ac9d2f67a5
  languageName: node
  linkType: hard

"@react-native/babel-preset@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/babel-preset@npm:0.79.2"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@babel/plugin-proposal-export-default-from": "npm:^7.24.7"
    "@babel/plugin-syntax-dynamic-import": "npm:^7.8.3"
    "@babel/plugin-syntax-export-default-from": "npm:^7.24.7"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
    "@babel/plugin-transform-arrow-functions": "npm:^7.24.7"
    "@babel/plugin-transform-async-generator-functions": "npm:^7.25.4"
    "@babel/plugin-transform-async-to-generator": "npm:^7.24.7"
    "@babel/plugin-transform-block-scoping": "npm:^7.25.0"
    "@babel/plugin-transform-class-properties": "npm:^7.25.4"
    "@babel/plugin-transform-classes": "npm:^7.25.4"
    "@babel/plugin-transform-computed-properties": "npm:^7.24.7"
    "@babel/plugin-transform-destructuring": "npm:^7.24.8"
    "@babel/plugin-transform-flow-strip-types": "npm:^7.25.2"
    "@babel/plugin-transform-for-of": "npm:^7.24.7"
    "@babel/plugin-transform-function-name": "npm:^7.25.1"
    "@babel/plugin-transform-literals": "npm:^7.25.2"
    "@babel/plugin-transform-logical-assignment-operators": "npm:^7.24.7"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.24.8"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.24.7"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.24.7"
    "@babel/plugin-transform-numeric-separator": "npm:^7.24.7"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.24.7"
    "@babel/plugin-transform-optional-catch-binding": "npm:^7.24.7"
    "@babel/plugin-transform-optional-chaining": "npm:^7.24.8"
    "@babel/plugin-transform-parameters": "npm:^7.24.7"
    "@babel/plugin-transform-private-methods": "npm:^7.24.7"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.24.7"
    "@babel/plugin-transform-react-display-name": "npm:^7.24.7"
    "@babel/plugin-transform-react-jsx": "npm:^7.25.2"
    "@babel/plugin-transform-react-jsx-self": "npm:^7.24.7"
    "@babel/plugin-transform-react-jsx-source": "npm:^7.24.7"
    "@babel/plugin-transform-regenerator": "npm:^7.24.7"
    "@babel/plugin-transform-runtime": "npm:^7.24.7"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.24.7"
    "@babel/plugin-transform-spread": "npm:^7.24.7"
    "@babel/plugin-transform-sticky-regex": "npm:^7.24.7"
    "@babel/plugin-transform-typescript": "npm:^7.25.2"
    "@babel/plugin-transform-unicode-regex": "npm:^7.24.7"
    "@babel/template": "npm:^7.25.0"
    "@react-native/babel-plugin-codegen": "npm:0.79.2"
    babel-plugin-syntax-hermes-parser: "npm:0.25.1"
    babel-plugin-transform-flow-enums: "npm:^0.0.2"
    react-refresh: "npm:^0.14.0"
  peerDependencies:
    "@babel/core": "*"
  checksum: 10c0/640c540b0e65a8d34e329d055c12df1c99860b792bfd0a8cfbbe8c4ad34568c9ab771ef6728419bdf42c4604e75e56a203d6216f83b7dd1e15f9803c95a57166
  languageName: node
  linkType: hard

"@react-native/codegen@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/codegen@npm:0.79.2"
  dependencies:
    glob: "npm:^7.1.1"
    hermes-parser: "npm:0.25.1"
    invariant: "npm:^2.2.4"
    nullthrows: "npm:^1.1.1"
    yargs: "npm:^17.6.2"
  peerDependencies:
    "@babel/core": "*"
  checksum: 10c0/50d80e005eb0bfedc277c1f48401f39522144372f31e92027119395238a409f8d476c8def75d731190bcd78d60c7d77ff5726c80ae10a7a8f4099762939a6870
  languageName: node
  linkType: hard

"@react-native/community-cli-plugin@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/community-cli-plugin@npm:0.79.2"
  dependencies:
    "@react-native/dev-middleware": "npm:0.79.2"
    chalk: "npm:^4.0.0"
    debug: "npm:^2.2.0"
    invariant: "npm:^2.2.4"
    metro: "npm:^0.82.0"
    metro-config: "npm:^0.82.0"
    metro-core: "npm:^0.82.0"
    semver: "npm:^7.1.3"
  peerDependencies:
    "@react-native-community/cli": "*"
  peerDependenciesMeta:
    "@react-native-community/cli":
      optional: true
  checksum: 10c0/0f1f3533256c83bf4cc50489f619a7d9fb668aaff7d34b1ad5fd2038479c85d0c309fd0c5b462959f076d44dd396547848f7706f4d89249937a1b0204094c7f0
  languageName: node
  linkType: hard

"@react-native/debugger-frontend@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/debugger-frontend@npm:0.79.2"
  checksum: 10c0/576bacad7d89c1af2e5c874f04fd380850ba34200a02b78120384fb0684bd468dd745b95af5adcc184ed070a444a0cf146a8633cfc66de054e9117ccaa115a4c
  languageName: node
  linkType: hard

"@react-native/dev-middleware@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/dev-middleware@npm:0.79.2"
  dependencies:
    "@isaacs/ttlcache": "npm:^1.4.1"
    "@react-native/debugger-frontend": "npm:0.79.2"
    chrome-launcher: "npm:^0.15.2"
    chromium-edge-launcher: "npm:^0.2.0"
    connect: "npm:^3.6.5"
    debug: "npm:^2.2.0"
    invariant: "npm:^2.2.4"
    nullthrows: "npm:^1.1.1"
    open: "npm:^7.0.3"
    serve-static: "npm:^1.16.2"
    ws: "npm:^6.2.3"
  checksum: 10c0/00fd4ad433c5a5d6c93a679d9ecbea529a005e1b8de01a4e1af935d3c3e8c8cb855ee574915a1cdb162c13f58f7ded357d46b893fb11b8460bda9d55a71477f0
  languageName: node
  linkType: hard

"@react-native/gradle-plugin@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/gradle-plugin@npm:0.79.2"
  checksum: 10c0/b0ceaa91662380a1d2826c6a2ccee300220815c54b919ca1c0a2c1897b5291b0f4fea45744b2867934b451d9f43117ce060b908fb65173c48883aaca0c8f0f8e
  languageName: node
  linkType: hard

"@react-native/js-polyfills@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/js-polyfills@npm:0.79.2"
  checksum: 10c0/87d646f7d0916a4ced9f2f90f388189d7df3a4ca992abdd7fa1795f2eed77bad3b1c763e2bc1765b7858c54a7db0ad8de8039c9d02e6489178ca10454a3443ee
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/normalize-colors@npm:0.79.2"
  checksum: 10c0/fd2b29273f8d2280fa3e2e7a0848ef3027e7c78e38136af163ca489df7dc0dda002f6df1b6b522a026f15975e60b626a1f1eb68bbd611056886b76e5c2f77e9f
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:^0.74.1":
  version: 0.74.89
  resolution: "@react-native/normalize-colors@npm:0.74.89"
  checksum: 10c0/6d0e5c91793ca5a66b4a0e5995361f474caacac56bde4772ac02b8ab470bd323076c567bd8856b0b097816d2b890e73a4040a3df01fd284adee683f5ba89d5ba
  languageName: node
  linkType: hard

"@react-native/virtualized-lists@npm:0.79.2":
  version: 0.79.2
  resolution: "@react-native/virtualized-lists@npm:0.79.2"
  dependencies:
    invariant: "npm:^2.2.4"
    nullthrows: "npm:^1.1.1"
  peerDependencies:
    "@types/react": ^19.0.0
    react: "*"
    react-native: "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/599ef30b30e29925134f4c0f8d1becb712e8774c0b495b743379fdf30ac97a27bd573c081dbb504aec4d29f0bc784e0e23777d5f16950e6bc95737c079a6a2b6
  languageName: node
  linkType: hard

"@react-navigation/bottom-tabs@npm:^7.3.10":
  version: 7.3.13
  resolution: "@react-navigation/bottom-tabs@npm:7.3.13"
  dependencies:
    "@react-navigation/elements": "npm:^2.4.2"
    color: "npm:^4.2.3"
  peerDependencies:
    "@react-navigation/native": ^7.1.9
    react: ">= 18.2.0"
    react-native: "*"
    react-native-safe-area-context: ">= 4.0.0"
    react-native-screens: ">= 4.0.0"
  checksum: 10c0/a078f5ca5186daa2e9ceafb23181d5a37a88ee047d0f95fa7e019be98155418525dbe6e8dc623751bb9fa3746e0270b6a75199d6e7b15d977f953284551a9b04
  languageName: node
  linkType: hard

"@react-navigation/core@npm:^7.9.2":
  version: 7.9.2
  resolution: "@react-navigation/core@npm:7.9.2"
  dependencies:
    "@react-navigation/routers": "npm:^7.3.7"
    escape-string-regexp: "npm:^4.0.0"
    nanoid: "npm:^3.3.11"
    query-string: "npm:^7.1.3"
    react-is: "npm:^19.1.0"
    use-latest-callback: "npm:^0.2.3"
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    react: ">= 18.2.0"
  checksum: 10c0/7753c929eb2d3026dd77d30c159323e099d1ae2dca1d6326c2df1d55b9e8437c05cd4885a320ddd545903f6aa1ff11658a81da63a817624b5587fef7c79f3b22
  languageName: node
  linkType: hard

"@react-navigation/elements@npm:^2.4.2":
  version: 2.4.2
  resolution: "@react-navigation/elements@npm:2.4.2"
  dependencies:
    color: "npm:^4.2.3"
  peerDependencies:
    "@react-native-masked-view/masked-view": ">= 0.2.0"
    "@react-navigation/native": ^7.1.9
    react: ">= 18.2.0"
    react-native: "*"
    react-native-safe-area-context: ">= 4.0.0"
  peerDependenciesMeta:
    "@react-native-masked-view/masked-view":
      optional: true
  checksum: 10c0/db40d4529c4d1fdffa129da741d41356524b9913e76b376e1b125aceb325eed9e4e4cb6fcef02cd9dc00da7ee28c41eca813900261c0a3b19a7ced369dbab34a
  languageName: node
  linkType: hard

"@react-navigation/native-stack@npm:^7.3.10":
  version: 7.3.13
  resolution: "@react-navigation/native-stack@npm:7.3.13"
  dependencies:
    "@react-navigation/elements": "npm:^2.4.2"
    warn-once: "npm:^0.1.1"
  peerDependencies:
    "@react-navigation/native": ^7.1.9
    react: ">= 18.2.0"
    react-native: "*"
    react-native-safe-area-context: ">= 4.0.0"
    react-native-screens: ">= 4.0.0"
  checksum: 10c0/041e0645a66bc0b91dbb2c50f7fd4763e250ae07f9beb33789c30bdc4659f663a9c6bde6cf930cbb67beebeed2c81e5b276040aca478ef30f2037e290f6e61c9
  languageName: node
  linkType: hard

"@react-navigation/native@npm:^7.0.14, @react-navigation/native@npm:^7.1.6":
  version: 7.1.9
  resolution: "@react-navigation/native@npm:7.1.9"
  dependencies:
    "@react-navigation/core": "npm:^7.9.2"
    escape-string-regexp: "npm:^4.0.0"
    fast-deep-equal: "npm:^3.1.3"
    nanoid: "npm:^3.3.11"
    use-latest-callback: "npm:^0.2.3"
  peerDependencies:
    react: ">= 18.2.0"
    react-native: "*"
  checksum: 10c0/844b1c593364d6355e5f64745ded5a262706e65f9cbf2201459ab0af3a261d97dc6a3e4d76f36ea9745638ef5f027b469370762a025855f890583bfd18ab589e
  languageName: node
  linkType: hard

"@react-navigation/routers@npm:^7.3.7":
  version: 7.3.7
  resolution: "@react-navigation/routers@npm:7.3.7"
  dependencies:
    nanoid: "npm:^3.3.11"
  checksum: 10c0/cf21d3f9e2245f95ae5f30d249ab720c09faf8faf66313ed23cbc1ef27121aa8b6ddadc9345f695f83f2a9d00c650dc8f5748d863b64ebb63bd0a383e3d3cde7
  languageName: node
  linkType: hard

"@react-stately/flags@npm:^3.1.1":
  version: 3.1.1
  resolution: "@react-stately/flags@npm:3.1.1"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10c0/3f64deb7b5daa787072f77e358624b65ad0627ee148d968423f3a5968b655d99671673927e21e4ba2ad0c9828d6ea75dae8ca421af90f9b41986a28341a4101f
  languageName: node
  linkType: hard

"@react-stately/overlays@npm:^3.1.1, @react-stately/overlays@npm:^3.6.15":
  version: 3.6.15
  resolution: "@react-stately/overlays@npm:3.6.15"
  dependencies:
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/overlays": "npm:^3.8.14"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/4e75602378869027bb752500c771a732e5c9d7963f8101eb03941b350e6b6a74c0da20ab75de9daa28e3fa10f7230952636957caf16953c8b70fa8eb836a4657
  languageName: node
  linkType: hard

"@react-stately/utils@npm:^3.10.6":
  version: 3.10.6
  resolution: "@react-stately/utils@npm:3.10.6"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/09403746285a3866765c04bed5f2505e0bdbb971bdeb2eedee31ebf5bad3d0c0c0cef9d0dd5852fc6c58f6b552cbc90364eb32403245dfc04dc22c5e2fbfbe32
  languageName: node
  linkType: hard

"@react-types/button@npm:^3.12.0":
  version: 3.12.0
  resolution: "@react-types/button@npm:3.12.0"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/6412c06f1a590581283d8cfe7661bbc9f1916b827ecb332fc7d65c918f9e52496fd11ce7859742532ab0d75b8746098e9fe04561efc700969d41e59179f15c30
  languageName: node
  linkType: hard

"@react-types/dialog@npm:*, @react-types/dialog@npm:^3.5.17":
  version: 3.5.17
  resolution: "@react-types/dialog@npm:3.5.17"
  dependencies:
    "@react-types/overlays": "npm:^3.8.14"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/44245c862f44a296e2692a757205aad40557040b1412e2afa1b22e77624772f699b1acb35e24b955f580df93cd84c85917e249ecda216ab53c4c33c0734c3c11
  languageName: node
  linkType: hard

"@react-types/overlays@npm:^3.4.0, @react-types/overlays@npm:^3.8.14":
  version: 3.8.14
  resolution: "@react-types/overlays@npm:3.8.14"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/a30a90997d40139a99d85b5a2f5e07ead48163e909f25e0ca1ff4664ebf8bd3bb59cebd3124b0eeba2e45226d36a54265eae5946dc587ec168acae47ed8f8090
  languageName: node
  linkType: hard

"@react-types/shared@npm:*, @react-types/shared@npm:^3.29.0":
  version: 3.29.0
  resolution: "@react-types/shared@npm:3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10c0/a629e4fe9ce9062de603a1e01ae90a999b07f1367143f3b66921c8a0c8e59d90a528263be74d930162ed4a78a725a253c48b6f3b00a85767549e86cac4cc8218
  languageName: node
  linkType: hard

"@shopify/react-native-skia@npm:v2.0.0-next.4":
  version: 2.0.0-next.4
  resolution: "@shopify/react-native-skia@npm:2.0.0-next.4"
  dependencies:
    canvaskit-wasm: "npm:0.40.0"
    react-reconciler: "npm:0.31.0"
  peerDependencies:
    react: ">=19.0"
    react-native: ">=0.78"
    react-native-reanimated: ^3.0
  peerDependenciesMeta:
    react-native:
      optional: true
    react-native-reanimated:
      optional: true
  bin:
    setup-skia-web: scripts/setup-canvaskit.js
  checksum: 10c0/a1b7be938dec0fa82d54d5271c6deed13bf149843ddb03cc3353cfd06f559fd8fec81d3d89dad21df0c1b99ba048615191bf99270d985cb6ccef22c4e099c3e0
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 10c0/ef6351ae073c45c2ac89494dbb3e1f87cc60a93ce4cde797b782812b6f97da0d620ae81973f104b43c9b7eaa789ad20ba4f6a1359f1cc62f63729a55a7d22d4e
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^3.0.0":
  version: 3.0.1
  resolution: "@sinonjs/commons@npm:3.0.1"
  dependencies:
    type-detect: "npm:4.0.8"
  checksum: 10c0/1227a7b5bd6c6f9584274db996d7f8cee2c8c350534b9d0141fc662eaf1f292ea0ae3ed19e5e5271c8fd390d27e492ca2803acd31a1978be2cdc6be0da711403
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.3.0
  resolution: "@sinonjs/fake-timers@npm:10.3.0"
  dependencies:
    "@sinonjs/commons": "npm:^3.0.0"
  checksum: 10c0/2e2fb6cc57f227912814085b7b01fede050cd4746ea8d49a1e44d5a0e56a804663b0340ae2f11af7559ea9bf4d087a11f2f646197a660ea3cb04e19efc04aa63
  languageName: node
  linkType: hard

"@smithy/abort-controller@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/abort-controller@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d5647478fa61d5d1cf3ac8fe5b91955c679ecf48e0d71638c0ce908fbcc87f166e42722d181f33ae3c37761de89e48c5eecf620f6fd0e99cd86edbb8365dd38d
  languageName: node
  linkType: hard

"@smithy/config-resolver@npm:^4.1.2":
  version: 4.1.2
  resolution: "@smithy/config-resolver@npm:4.1.2"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/fb7b0c027d7b200807b8a3dc023be56602fcf7203b2d2e1acc2aa6cd47b3317f9d54a90c4ff133836a2d0bc79b10d70d5d3b6356ac40a53d58e422921fb8b524
  languageName: node
  linkType: hard

"@smithy/core@npm:^3.3.3":
  version: 3.3.3
  resolution: "@smithy/core@npm:3.3.3"
  dependencies:
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-stream": "npm:^4.2.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/745f823da82b0f6bf48cfbce8c0c5caa1eaec94d400a9c5ca7a2d965252e957b0fc700c0085bcbd91222a73082330c2a1088035904b44e3bca43e485faba1e18
  languageName: node
  linkType: hard

"@smithy/credential-provider-imds@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/credential-provider-imds@npm:4.0.4"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5200503b7879bbe8beb959713c54b4e23788a2f2823a7b27d8332b7c3864dbcbadda8cbc4adfdfd4b74c0423ce153ff5fb51701197748636c4cc1c35cf8f7808
  languageName: node
  linkType: hard

"@smithy/fetch-http-handler@npm:^5.0.2":
  version: 5.0.2
  resolution: "@smithy/fetch-http-handler@npm:5.0.2"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/querystring-builder": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-base64": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/3bf84a1fe93c07558a5ba520ab0aca62518c13659d5794094764aaef95acfbcf58ba938c51b9269c485304fdbe7353eb3cd37d7e4c57863d7c50478a9e3ff4fc
  languageName: node
  linkType: hard

"@smithy/hash-node@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/hash-node@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/aaec3fb2146d4347e97067de4dd91759de9d0254d03e234dcced1cbd52cf8b3a77067d571bd5767cb6295da7aa7261b87a789bd597cbc45a380cd90bb47f3490
  languageName: node
  linkType: hard

"@smithy/invalid-dependency@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/invalid-dependency@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/f0b884ba25c371d3d3f507aebc24e598e23edeadf0a74dfd7092fc49c496cd427ab517454ebde454b2a05916719e01aa98f34603a5396455cc2dc009ad8799e8
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/is-array-buffer@npm:2.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/2f2523cd8cc4538131e408eb31664983fecb0c8724956788b015aaf3ab85a0c976b50f4f09b176f1ed7bbe79f3edf80743be7a80a11f22cd9ce1285d77161aaf
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/is-array-buffer@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/ae393fbd5944d710443cd5dd225d1178ef7fb5d6259c14f3e1316ec75e401bda6cf86f7eb98bfd38e5ed76e664b810426a5756b916702cbd418f0933e15e7a3b
  languageName: node
  linkType: hard

"@smithy/middleware-content-length@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/middleware-content-length@npm:4.0.2"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/4ab343b68a15cf461f3b5996460a0730463975d9da739cf40cfb5993794023269a8bd857366f855844290fabb2b340abb6ff473cec4bfd3d6653a64f17e00c4a
  languageName: node
  linkType: hard

"@smithy/middleware-endpoint@npm:^4.1.6":
  version: 4.1.6
  resolution: "@smithy/middleware-endpoint@npm:4.1.6"
  dependencies:
    "@smithy/core": "npm:^3.3.3"
    "@smithy/middleware-serde": "npm:^4.0.5"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/url-parser": "npm:^4.0.2"
    "@smithy/util-middleware": "npm:^4.0.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/2a2d499470aa44021f5651185affb96776a5f58e0107f58318e85520b869049cf485b7eb95bd9fd0eeaceb3c660eeaab6e63616dd9ad93c1be334271e7f9889e
  languageName: node
  linkType: hard

"@smithy/middleware-retry@npm:^4.1.7":
  version: 4.1.7
  resolution: "@smithy/middleware-retry@npm:4.1.7"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/service-error-classification": "npm:^4.0.3"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-retry": "npm:^4.0.3"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10c0/c47e4723fa8d69563e99c0e89f3b4e1787b52a83fd2894a4e6cc19f0e628bc0eac892516061dabefb0f5d86de68425e3651bddf2fe9925d08c01228d5dc4a67b
  languageName: node
  linkType: hard

"@smithy/middleware-serde@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/middleware-serde@npm:4.0.5"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/14897e4d433d75956d6c9a2af15e192802153ecc9a65af058fa43c535278682358a5020744db5e3bff9ee4fd444ad495bb0e83358cf9434fa31ca0f52e5851eb
  languageName: node
  linkType: hard

"@smithy/middleware-stack@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/middleware-stack@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ef94882966431729f7a7bddf8206b6495d67736b1f26fd88d6d6c283a96f9fffd12632ed7352e5f060f17d3ee1845a9a9da1247c26e4c46ff7011aac20b4aacc
  languageName: node
  linkType: hard

"@smithy/node-config-provider@npm:^4.1.1":
  version: 4.1.1
  resolution: "@smithy/node-config-provider@npm:4.1.1"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/shared-ini-file-loader": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/05db1a08ac866ad2b5fd28da81b081b0711b03057af18b7e08d3b41942b710ad2f0cf762b1806d85246fa12fee3f063eeb56d067b7517c12f2fe9cd7a54d6554
  languageName: node
  linkType: hard

"@smithy/node-http-handler@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/node-http-handler@npm:4.0.4"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/querystring-builder": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/fb621c6ebcf012a99fc442d82965ca18d752f66be6f937a400e3b4e3feef1c259c028c27df9e78fc9ac7c40679b25276cbaa8d7ab82fd111bda64003ef831358
  languageName: node
  linkType: hard

"@smithy/property-provider@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/property-provider@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6effc5ef7895eb4802c6b4c704d5616f50cd0c376da1644176d3aef71396cb65f9df20f4dd85c8301a9fa24f8ac53601e0634463f4364f0d867928efa5eb5e3d
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^5.1.0":
  version: 5.1.0
  resolution: "@smithy/protocol-http@npm:5.1.0"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/bb2f600853c0282630f5f32286a07a37294a57dbbec25ea0c6fbb6be32341b1be83e37933c2e3540e513c90dcb08f492bcb05980cde0b92b083e67ade6d56eb0
  languageName: node
  linkType: hard

"@smithy/querystring-builder@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/querystring-builder@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/2ae27840e21982926182df809872e07d6b10b2fd93b58e02fa3f9588de23d333ddf02f0f3517de8a02a949489733bdcecb8c847980f8fb12ce1f8c3b6d127e86
  languageName: node
  linkType: hard

"@smithy/querystring-parser@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/querystring-parser@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e6115fce0a07b1509f407cd3eca371cce1d9c09c7e3bd9156e35506b8ab1100f9864fb8779d4dbe0169501af23f062ebc2176afc012e9132e917781cd11a2f82
  languageName: node
  linkType: hard

"@smithy/service-error-classification@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/service-error-classification@npm:4.0.3"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
  checksum: 10c0/bc8a1239f2176fc0e980624e189871b309b0d61c5652022df236f34cd96e97f15719fd44c9d74cf2e1b632f5620a0fcccc6db77dbf9452bcec4e427456d96563
  languageName: node
  linkType: hard

"@smithy/shared-ini-file-loader@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/shared-ini-file-loader@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1e3d4921b6efbd1aa448a775dcb9a490d0221dd0a4fee434c5d83376de478013b3ad06d58a3d52db781124d4a53bd289fffcdb52eabffe9de152b0010332cee2
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^5.1.0":
  version: 5.1.0
  resolution: "@smithy/signature-v4@npm:5.1.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.2"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/7f3aed4999b47f04485846a90a08d0863c8bf4201a38616faf4bcb3166892a5b2946e7d0f1d5dc068b667913713873e21ab8374d60c1ff02828972d8c9201282
  languageName: node
  linkType: hard

"@smithy/smithy-client@npm:^4.2.6":
  version: 4.2.6
  resolution: "@smithy/smithy-client@npm:4.2.6"
  dependencies:
    "@smithy/core": "npm:^3.3.3"
    "@smithy/middleware-endpoint": "npm:^4.1.6"
    "@smithy/middleware-stack": "npm:^4.0.2"
    "@smithy/protocol-http": "npm:^5.1.0"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-stream": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a5e302f925ce9540a6a74f5f7d24c6a18bbdd1688faf19fa4177a091c78221d1558568f507268e2799b5ef168476067fd77c901af841a3d3e78ddfcabc03d7d4
  languageName: node
  linkType: hard

"@smithy/types@npm:^4.2.0":
  version: 4.2.0
  resolution: "@smithy/types@npm:4.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/a8bd92c7e548bcbe7be211152de041ec164cfcc857d7574a87b1667c38827e5616563c13bd38a1d44b88bbfa3ee8f591dc597d4e2d50f3bc74e320ea82d7c49e
  languageName: node
  linkType: hard

"@smithy/url-parser@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/url-parser@npm:4.0.2"
  dependencies:
    "@smithy/querystring-parser": "npm:^4.0.2"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/3da40fc18871c145bcbbb036a3d767ae113b954e94c745770f268dc877378cbafa6fc06759ea5a5e5c159a88e7331739b35b69f4d110ba0bd04b2d0923443f32
  languageName: node
  linkType: hard

"@smithy/util-base64@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-base64@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ad18ec66cc357c189eef358d96876b114faf7086b13e47e009b265d0ff80cec046052500489c183957b3a036768409acdd1a373e01074cc002ca6983f780cffc
  languageName: node
  linkType: hard

"@smithy/util-body-length-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-browser@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/574a10934024a86556e9dcde1a9776170284326c3dfcc034afa128cc5a33c1c8179fca9cfb622ef8be5f2004316cc3f427badccceb943e829105536ec26306d9
  languageName: node
  linkType: hard

"@smithy/util-body-length-node@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-node@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/e91fd3816767606c5f786166ada26440457fceb60f96653b3d624dcf762a8c650e513c275ff3f647cb081c63c283cc178853a7ed9aa224abc8ece4eeeef7a1dd
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/util-buffer-from@npm:2.2.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/223d6a508b52ff236eea01cddc062b7652d859dd01d457a4e50365af3de1e24a05f756e19433f6ccf1538544076b4215469e21a4ea83dc1d58d829725b0dbc5a
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-buffer-from@npm:4.0.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/be7cd33b6cb91503982b297716251e67cdca02819a15797632091cadab2dc0b4a147fff0709a0aa9bbc0b82a2644a7ed7c8afdd2194d5093cee2e9605b3a9f6f
  languageName: node
  linkType: hard

"@smithy/util-config-provider@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-config-provider@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/cd9498d5f77a73aadd575084bcb22d2bb5945bac4605d605d36f2efe3f165f2b60f4dc88b7a62c2ed082ffa4b2c2f19621d0859f18399edbc2b5988d92e4649f
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-browser@npm:^4.0.14":
  version: 4.0.14
  resolution: "@smithy/util-defaults-mode-browser@npm:4.0.14"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0579d1eaaab06001d41806e6031d3faa6a0f1f937be06be5ceef054dcaa462a8f74a6ad0d55e3e207a9574ee17443dd7e98d8575306746dc214808d01feb2593
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-node@npm:^4.0.14":
  version: 4.0.14
  resolution: "@smithy/util-defaults-mode-node@npm:4.0.14"
  dependencies:
    "@smithy/config-resolver": "npm:^4.1.2"
    "@smithy/credential-provider-imds": "npm:^4.0.4"
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/property-provider": "npm:^4.0.2"
    "@smithy/smithy-client": "npm:^4.2.6"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c246a59c985b409e867f6bc4696b29973016f9dfbbfb44b631b846c9154677abcf2e0f8a582839d871347915ed9c4ed77f278306757cb53e5962230350b7b747
  languageName: node
  linkType: hard

"@smithy/util-endpoints@npm:^3.0.4":
  version: 3.0.4
  resolution: "@smithy/util-endpoints@npm:3.0.4"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.1"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/76c980e42da9d113e768d2638c1cfcb3e90dacb24cd47a443a97f3a70cc13bc56ba27af79465fa8dbf561fb2f028c2e19bed4e5296ace4f9e6b2082ee0a7ae1f
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-hex-encoding@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/70dbb3aa1a79aff3329d07a66411ff26398df338bdd8a6d077b438231afe3dc86d9a7022204baddecd8bc633f059d5c841fa916d81dd7447ea79b64148f386d2
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/util-middleware@npm:4.0.2"
  dependencies:
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/18c3882c94f1b1bbb3825c30d1e41ae77a8da3dcd93ebbf1c486f34d5db9e06431789aef54d1b1fbb0424b115fc1e1ae17d27efe4af4277173d901a76147fef8
  languageName: node
  linkType: hard

"@smithy/util-retry@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/util-retry@npm:4.0.3"
  dependencies:
    "@smithy/service-error-classification": "npm:^4.0.3"
    "@smithy/types": "npm:^4.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a00c47182efed65f3a86afc28fef69ae1e083965cc69f18bc82fb86e23179c3d083639c4819c97a625924b207dc1efaf7b68cf52e1c030f7c9a9625bbea215e0
  languageName: node
  linkType: hard

"@smithy/util-stream@npm:^4.2.0":
  version: 4.2.0
  resolution: "@smithy/util-stream@npm:4.2.0"
  dependencies:
    "@smithy/fetch-http-handler": "npm:^5.0.2"
    "@smithy/node-http-handler": "npm:^4.0.4"
    "@smithy/types": "npm:^4.2.0"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/52449a6ec68a483fdeef816128c923c744e278f6cf4d5b6fbe50e29fa8b6e5813df26221389f22bce143deb91f047ac56f87db85306908c5d0b87460e162bf63
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-uri-escape@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/23984624060756adba8aa4ab1693fe6b387ee5064d8ec4dfd39bb5908c4ee8b9c3f2dc755da9b07505d8e3ce1338c1867abfa74158931e4728bf3cfcf2c05c3d
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^2.0.0":
  version: 2.3.0
  resolution: "@smithy/util-utf8@npm:2.3.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e18840c58cc507ca57fdd624302aefd13337ee982754c9aa688463ffcae598c08461e8620e9852a424d662ffa948fc64919e852508028d09e89ced459bd506ab
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-utf8@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/28a5a5372cbf0b3d2e32dd16f79b04c2aec6f704cf13789db922e9686fde38dde0171491cfa4c2c201595d54752a319faaeeed3c325329610887694431e28c98
  languageName: node
  linkType: hard

"@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a50bd0baa34faf16bcba712091f94c7f0e230431fe99a9dfc3401fa92823ad3f68495b86ab9bf9044b53839e8c416cfbb37eb3f246ff33f261e0fa9ee1779c5b
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/8a98e59bd9971e066815b4129409932f7a4db4866834fe75677ea6d517972fb40b380a69a4413189f20e7947411f9ab1b0f029dd5e8068686a5a0188d3ccd4c7
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/517dcca75223bd05d3f056a8514dbba3031278bea4eadf0842c576d84f4651e7a4e0e7082d3ee4ef42456de0f9c4531d8a1917c04876ca64b014b859ca8f1bde
  languageName: node
  linkType: hard

"@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/004bd1892053b7e9c1b0bb14acc44e77634ec393722b87b1e4fae53e2c35122a2dd0d5c15e9070dbeec274e22e7693a2b8b48506733a8009ee92b12946fcb10a
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/80e0a7fcf902f984c705051ca5c82ea6050ccbb70b651a8fea6d0eb5809e4dac274b49ea6be2d87f1eb9dfc0e2d6cdfffe1669ec2117f44b67a60a07d4c0b8b8
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/73e92c8277a89279745c0c500f59f083279a8dc30cd552b22981fade2a77628fb2bd2819ee505725fcd2e93f923e3790b52efcff409a159e657b46604a0b9a21
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/655ed6bc7a208ceaa4ecff0a54ccc36008c3cb31efa90d11e171cab325ebbb21aa78f09c7b65f9b3ddeda3a85f348c0c862902c48be13c14b4de165c847974e3
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-svg-component@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-transform-svg-component@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4ac00bb99a3db4ef05e4362f116a3c608ee365a2d26cf7318d8d41a4a5b30a02c80455cce0e62c65b60ed815b5d632bedabac2ccd4b56f998fadef5286e3ded4
  languageName: node
  linkType: hard

"@svgr/babel-preset@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-preset@npm:8.1.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute": "npm:8.0.0"
    "@svgr/babel-plugin-remove-jsx-attribute": "npm:8.0.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression": "npm:8.0.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value": "npm:8.0.0"
    "@svgr/babel-plugin-svg-dynamic-title": "npm:8.0.0"
    "@svgr/babel-plugin-svg-em-dimensions": "npm:8.0.0"
    "@svgr/babel-plugin-transform-react-native-svg": "npm:8.1.0"
    "@svgr/babel-plugin-transform-svg-component": "npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/49367d3ad0831f79b1056871b91766246f449d4d1168623af5e283fbaefce4a01d77ab00de6b045b55e956f9aae27895823198493cd232d88d3435ea4517ffc5
  languageName: node
  linkType: hard

"@svgr/core@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/core@npm:8.1.0"
  dependencies:
    "@babel/core": "npm:^7.21.3"
    "@svgr/babel-preset": "npm:8.1.0"
    camelcase: "npm:^6.2.0"
    cosmiconfig: "npm:^8.1.3"
    snake-case: "npm:^3.0.4"
  checksum: 10c0/6a2f6b1bc79bce39f66f088d468985d518005fc5147ebf4f108570a933818b5951c2cb7da230ddff4b7c8028b5a672b2d33aa2acce012b8b9770073aa5a2d041
  languageName: node
  linkType: hard

"@svgr/hast-util-to-babel-ast@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/hast-util-to-babel-ast@npm:8.0.0"
  dependencies:
    "@babel/types": "npm:^7.21.3"
    entities: "npm:^4.4.0"
  checksum: 10c0/f4165b583ba9eaf6719e598977a7b3ed182f177983e55f9eb55a6a73982d81277510e9eb7ab41f255151fb9ed4edd11ac4bef95dd872f04ed64966d8c85e0f79
  languageName: node
  linkType: hard

"@svgr/plugin-jsx@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-jsx@npm:8.1.0"
  dependencies:
    "@babel/core": "npm:^7.21.3"
    "@svgr/babel-preset": "npm:8.1.0"
    "@svgr/hast-util-to-babel-ast": "npm:8.0.0"
    svg-parser: "npm:^2.0.4"
  peerDependencies:
    "@svgr/core": "*"
  checksum: 10c0/07b4d9e00de795540bf70556fa2cc258774d01e97a12a26234c6fdf42b309beb7c10f31ee24d1a71137239347b1547b8bb5587d3a6de10669f95dcfe99cddc56
  languageName: node
  linkType: hard

"@svgr/plugin-svgo@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-svgo@npm:8.1.0"
  dependencies:
    cosmiconfig: "npm:^8.1.3"
    deepmerge: "npm:^4.3.1"
    svgo: "npm:^3.0.2"
  peerDependencies:
    "@svgr/core": "*"
  checksum: 10c0/bfd25460f23f1548bfb8f6f3bedd6d6972c1a4f8881bd35a4f8c115218da6e999e8f9ac0ef0ed88c4e0b93fcec37f382b94c0322f4ec2b26752a89e5cc8b9d7a
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.5.0":
  version: 0.5.17
  resolution: "@swc/helpers@npm:0.5.17"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10c0/fe1f33ebb968558c5a0c595e54f2e479e4609bff844f9ca9a2d1ffd8dd8504c26f862a11b031f48f75c95b0381c2966c3dd156e25942f90089badd24341e7dbb
  languageName: node
  linkType: hard

"@tanstack/eslint-plugin-query@npm:4":
  version: 4.38.0
  resolution: "@tanstack/eslint-plugin-query@npm:4.38.0"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/cf6ee6b8992b37edf6c833755616f1d784449c3300900dbf775cd2b6eba87a7eb86cfb3aa2afb2ff0ef56b70f9e2fa5c8ed5ed498e624fc3d0cea77c18ffe222
  languageName: node
  linkType: hard

"@tanstack/query-core@npm:5.76.0":
  version: 5.76.0
  resolution: "@tanstack/query-core@npm:5.76.0"
  checksum: 10c0/970cc783838bf7e30acbad476c72e4bb3b302c15525152cfcf5ffef8b185537e780b608e3f77793cb355b49cc98a9de597d978d400df44dbd550cba78742d67c
  languageName: node
  linkType: hard

"@tanstack/react-query@npm:^5.53.1":
  version: 5.76.1
  resolution: "@tanstack/react-query@npm:5.76.1"
  dependencies:
    "@tanstack/query-core": "npm:5.76.0"
  peerDependencies:
    react: ^18 || ^19
  checksum: 10c0/35757f185ccd577e0763b18c2e29db621b40fb85419507d43a2a136fd47d8a4410f219fa9697142f7199577cc42c23dd117021cc8c072545eca133d5114148d9
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: 10c0/073bfa548026b1ebaf1659eb8961e526be22fa77139b10d60e712f46d2f0f05f4e6c8bec62a087d41088ee9e29faa7f54838568e475ab2f776171003c3920858
  languageName: node
  linkType: hard

"@trivago/prettier-plugin-sort-imports@npm:^5.2.1":
  version: 5.2.2
  resolution: "@trivago/prettier-plugin-sort-imports@npm:5.2.2"
  dependencies:
    "@babel/generator": "npm:^7.26.5"
    "@babel/parser": "npm:^7.26.7"
    "@babel/traverse": "npm:^7.26.7"
    "@babel/types": "npm:^7.26.7"
    javascript-natural-sort: "npm:^0.7.1"
    lodash: "npm:^4.17.21"
  peerDependencies:
    "@vue/compiler-sfc": 3.x
    prettier: 2.x - 3.x
    prettier-plugin-svelte: 3.x
    svelte: 4.x || 5.x
  peerDependenciesMeta:
    "@vue/compiler-sfc":
      optional: true
    prettier-plugin-svelte:
      optional: true
    svelte:
      optional: true
  checksum: 10c0/2a4f0464f1f5a294bcd34558fb053f8263f0c62c4a7fcdd3ce40c9822a68ac8b4d951700ab6d01eb3919efe0ed44e4191997edd494d59679b22db1c0db00474e
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 10c0/44907308549ce775a41c38a815f747009ac45929a45d642b836aa6b0a536e4978d30b8d7d680bbd116e9dd73b7dbe2ef0d1369dcfc2d09e83ba381e485ecbe12
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10c0/bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/9f9e959a8792df208a9d048092fda7e1858bddc95c6314857a8211a99e20e6830bdeb572e3587ae8be5429e37f2a96fcf222a9f53ad232f5537764c9e13a2bbd
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.7
  resolution: "@types/babel__traverse@npm:7.20.7"
  dependencies:
    "@babel/types": "npm:^7.20.7"
  checksum: 10c0/5386f0af44f8746b063b87418f06129a814e16bb2686965a575e9d7376b360b088b89177778d8c426012abc43dd1a2d8ec3218bfc382280c898682746ce2ffbd
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/2e1cdba2c410f25649e77856505cd60223250fa12dff7a503e492208dbfdd25f62859918f28aba95315251fd1f5e1ffbfca1e25e73037189ab85dd3f8d0a148c
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.9
  resolution: "@types/graceful-fs@npm:4.1.9"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/235d2fc69741448e853333b7c3d1180a966dd2b8972c8cbcd6b2a0c6cd7f8d582ab2b8e58219dbc62cce8f1b40aa317ff78ea2201cdd8249da5025adebed6f0b
  languageName: node
  linkType: hard

"@types/hammerjs@npm:^2.0.36":
  version: 2.0.46
  resolution: "@types/hammerjs@npm:2.0.46"
  checksum: 10c0/f3c1cb20dc2f0523f7b8c76065078544d50d8ae9b0edc1f62fed657210ed814266ff2dfa835d2c157a075991001eec3b64c88bf92e3e6e895c0db78d05711d06
  languageName: node
  linkType: hard

"@types/hoist-non-react-statics@npm:*":
  version: 3.3.6
  resolution: "@types/hoist-non-react-statics@npm:3.3.6"
  dependencies:
    "@types/react": "npm:*"
    hoist-non-react-statics: "npm:^3.3.0"
  checksum: 10c0/149a4c217d81f21f8a1e152160a59d5b99b6a9aa6d354385d5f5bc02760cbf1e170a8442ba92eb653befff44b0c5bc2234bb77ce33e0d11a65f779e8bab5c321
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 10c0/3948088654f3eeb45363f1db158354fb013b362dba2a5c2c18c559484d5eb9f6fd85b23d66c0a7c2fcfab7308d0a585b14dadaca6cc8bf89ebfdc7f8f5102fb7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
  checksum: 10c0/247e477bbc1a77248f3c6de5dadaae85ff86ac2d76c5fc6ab1776f54512a745ff2a5f791d22b942e3990ddbd40f3ef5289317c4fca5741bedfaa4f01df89051c
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "npm:*"
  checksum: 10c0/1647fd402aced5b6edac87274af14ebd6b3a85447ef9ad11853a70fd92a98d35f81a5d3ea9fcb5dbb5834e800c6e35b64475e33fcae6bfa9acc70d61497c54ee
  languageName: node
  linkType: hard

"@types/jquery@npm:*":
  version: 3.5.32
  resolution: "@types/jquery@npm:3.5.32"
  dependencies:
    "@types/sizzle": "npm:*"
  checksum: 10c0/4a17ad6819b89026c21323656ab01b0b263f9d470910a87c8740920ff98319d503c7352b85b50134a39724ecbfccabc73aa4c741dfdd460cf8bbe714f9259054
  languageName: node
  linkType: hard

"@types/jsdom@npm:^20.0.0":
  version: 20.0.1
  resolution: "@types/jsdom@npm:20.0.1"
  dependencies:
    "@types/node": "npm:*"
    "@types/tough-cookie": "npm:*"
    parse5: "npm:^7.0.0"
  checksum: 10c0/3d4b2a3eab145674ee6da482607c5e48977869109f0f62560bf91ae1a792c9e847ac7c6aaf243ed2e97333cb3c51aef314ffa54a19ef174b8f9592dfcb836b25
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/meteor@npm:^2.9.8":
  version: 2.9.8
  resolution: "@types/meteor@npm:2.9.8"
  dependencies:
    "@types/connect": "npm:*"
    "@types/jquery": "npm:*"
    "@types/node": "npm:*"
    "@types/nodemailer": "npm:*"
    "@types/react": "npm:*"
    "@types/underscore": "npm:*"
    mongodb: "npm:^4.3.1"
  checksum: 10c0/07617bfcd325216c61fc15b42a1e1b5acf750870785450ee242dd97ae226d2460377768f9c13924a721a913bc86ff090dd90546e90aae5807507d85e8a4e1880
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.15.19
  resolution: "@types/node@npm:22.15.19"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10c0/8ef52fa1a91b1c8891616d46f3921a9f3c65ad1c6bb62db7899c8c28643c13bf9d607a2403b1e5aceb3e6fa6749efc9e0ba5c39618a4872da6946437b0edbfbe
  languageName: node
  linkType: hard

"@types/nodemailer@npm:*":
  version: 6.4.17
  resolution: "@types/nodemailer@npm:6.4.17"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/689abb3005cf36cf89c2abe56f0aa4469a37e0814633a73fbeb35732e856f4b0d7ab32b6d91585038b6941f5b70db58ec2bd147ebe9f73e528eb6c99604f4e82
  languageName: node
  linkType: hard

"@types/react-native@npm:^0.70":
  version: 0.70.19
  resolution: "@types/react-native@npm:0.70.19"
  dependencies:
    "@types/react": "npm:*"
  checksum: 10c0/09a5191dd0f3b85abfa8939df585860ba11a666d888846c7fd7d8d5d1cd72a39224e154403f90a5b82f1dc6c896fa3447e4f5061b2d3e102d0ce798e559d7f13
  languageName: node
  linkType: hard

"@types/react@npm:*":
  version: 19.1.4
  resolution: "@types/react@npm:19.1.4"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/501350d4f9cef13c5dd1b1496fa70ebaff52f6fa359b623b51c9d817e5bc4333fa3c8b7a6a4cbc88c643385052d66a243c3ceccfd6926062f917a2dd0535f6b3
  languageName: node
  linkType: hard

"@types/react@npm:~19.0.10":
  version: 19.0.14
  resolution: "@types/react@npm:19.0.14"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/e5d9ac42fc6d66c21b7020c8ae1a8190c486e63e5daf2f67b67694dd39c6264cc92a57f90b84525bf73774b90f91bd3b1d907022bcc9b36d6d4ffbcf001f8feb
  languageName: node
  linkType: hard

"@types/sizzle@npm:*":
  version: 2.3.9
  resolution: "@types/sizzle@npm:2.3.9"
  checksum: 10c0/db0277ff62e8ebe6cdae2020fd045fd7fd19f29a3a2ce13c555b14fb00e105e79004883732118b9f2e8b943cb302645e9eddb4e7bdeef1a171da679cd4c32b72
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 10c0/1f4658385ae936330581bcb8aa3a066df03867d90281cdf89cc356d404bd6579be0f11902304e1f775d92df22c6dd761d4451c804b0a4fba973e06211e9bd77c
  languageName: node
  linkType: hard

"@types/strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "@types/strip-bom@npm:3.0.0"
  checksum: 10c0/6638635fb52dc1f7a4aa596445170ffc731f3bea307d25d79709dcce14f80870128a6f0304032863b9d1a86b4b5f45d48bcaf96abe81f42e61f0a3eb18a1b996
  languageName: node
  linkType: hard

"@types/strip-json-comments@npm:0.0.30":
  version: 0.0.30
  resolution: "@types/strip-json-comments@npm:0.0.30"
  checksum: 10c0/90509e345ac16c79f7aa7d7ef52e388e5be923f3456cf8052d36ee0eb4abc5ec4080c5f010f78cf01f5599546577eb3724256bc698663e86f0fe08a5a3fb7f68
  languageName: node
  linkType: hard

"@types/styled-components-react-native@npm:^5.2.5":
  version: 5.2.5
  resolution: "@types/styled-components-react-native@npm:5.2.5"
  dependencies:
    "@types/react": "npm:*"
    "@types/react-native": "npm:^0.70"
    "@types/styled-components": "npm:*"
  checksum: 10c0/1223c567c0b8588cd0dc3ad85204f0e712588867240e637f69f2419c2d38dc9e55629926320df5065a81d01322e37888ec971dc97ff4c143f55f247f14994992
  languageName: node
  linkType: hard

"@types/styled-components@npm:*":
  version: 5.1.34
  resolution: "@types/styled-components@npm:5.1.34"
  dependencies:
    "@types/hoist-non-react-statics": "npm:*"
    "@types/react": "npm:*"
    csstype: "npm:^3.0.2"
  checksum: 10c0/5bce93ea2c6161fc45daaf863eefdc20672e839ae486597c40b95e7978e249c160c1bc9706f56cb5152a7ef63cf485d15a9502889169ef945281f511e4b2d5a0
  languageName: node
  linkType: hard

"@types/stylis@npm:4.2.5":
  version: 4.2.5
  resolution: "@types/stylis@npm:4.2.5"
  checksum: 10c0/23f5b35a3a04f6bb31a29d404fa1bc8e0035fcaff2356b4047743a057e0c37b2eba7efe14d57dd2b95b398cea3bac294d9c6cd93ed307d8c0b7f5d282224b469
  languageName: node
  linkType: hard

"@types/tough-cookie@npm:*":
  version: 4.0.5
  resolution: "@types/tough-cookie@npm:4.0.5"
  checksum: 10c0/68c6921721a3dcb40451543db2174a145ef915bc8bcbe7ad4e59194a0238e776e782b896c7a59f4b93ac6acefca9161fccb31d1ce3b3445cb6faa467297fb473
  languageName: node
  linkType: hard

"@types/underscore@npm:*":
  version: 1.13.0
  resolution: "@types/underscore@npm:1.13.0"
  checksum: 10c0/240d3f36f694e177b1896c464b1254249e64b51a2afc703a1dda61f0c544d6e3b081ac4d955fb057e873982a62a7ba78e3a0aaa252c9d766f6cbe5e85283bc04
  languageName: node
  linkType: hard

"@types/webidl-conversions@npm:*":
  version: 7.0.3
  resolution: "@types/webidl-conversions@npm:7.0.3"
  checksum: 10c0/ac2ccff93b95ac7c8ca73dc6064403181691bba7ea144296f462dc9108a07be16cbad7b9c704b3df706dcc5a117e1f7bf7fb27aeb75b09c0f3148de8aee11aff
  languageName: node
  linkType: hard

"@types/whatwg-url@npm:^8.2.1":
  version: 8.2.2
  resolution: "@types/whatwg-url@npm:8.2.2"
  dependencies:
    "@types/node": "npm:*"
    "@types/webidl-conversions": "npm:*"
  checksum: 10c0/7e5b6837daff8c6d189b13d19cc6d69e3bf954751f4f8a92d9a762c7f32ba75464d8e686a69c7d70e5092499c8ac14933c0ed416cf563689b04c4e10bff95e40
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: 10c0/e71c3bd9d0b73ca82e10bee2064c384ab70f61034bbfb78e74f5206283fc16a6d85267b606b5c22cb2a3338373586786fed595b2009825d6a9115afba36560a0
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10c0/d16937d7ac30dff697801c3d6f235be2166df42e4a88bf730fa6dc09201de3727c0a9500c59a672122313341de5f24e45ee0ff579c08ce91928e519090b7906b
  languageName: node
  linkType: hard

"@urql/core@npm:^5.0.6, @urql/core@npm:^5.1.1":
  version: 5.1.1
  resolution: "@urql/core@npm:5.1.1"
  dependencies:
    "@0no-co/graphql.web": "npm:^1.0.5"
    wonka: "npm:^6.3.2"
  checksum: 10c0/2a66f58452bbf153c251dd6d127fc0bc0473b4cde47171ca360960059eb08fc019202aee16911168a800814a3b9748300bb88b87817b5d05cf92c16f5772447b
  languageName: node
  linkType: hard

"@urql/exchange-retry@npm:^1.3.0":
  version: 1.3.1
  resolution: "@urql/exchange-retry@npm:1.3.1"
  dependencies:
    "@urql/core": "npm:^5.1.1"
    wonka: "npm:^6.3.2"
  peerDependencies:
    "@urql/core": ^5.0.0
  checksum: 10c0/c7d0e5e31de3ad3ff169ca5a2f635be03057dd151a9d2ef7ff2c8a605c4d1129f8a2b7671fc9adf90aef69c963a0cc08d32c62f8fe60f220d412757e71916806
  languageName: node
  linkType: hard

"@webgpu/types@npm:0.1.21":
  version: 0.1.21
  resolution: "@webgpu/types@npm:0.1.21"
  checksum: 10c0/74de9683b70064aeadcdd31381747650b323e7e2183fb38f90a35f1a79f80ad267c0a2ee5010c264befb3d31ad213dbdffa04897e2808f6b04feff8d66bc53e0
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.8":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 10c0/c7647c442502720182b0d65b17d45d2d95317c1c8c497626fe524bda79b4fb768a9aa4fae2da919f308e7abcff7d67c058b102a9d641097e9a57f0b80187851f
  languageName: node
  linkType: hard

"@yarnpkg/lockfile@npm:^1.1.0":
  version: 1.1.0
  resolution: "@yarnpkg/lockfile@npm:1.1.0"
  checksum: 10c0/0bfa50a3d756623d1f3409bc23f225a1d069424dbc77c6fd2f14fb377390cd57ec703dc70286e081c564be9051ead9ba85d81d66a3e68eeb6eb506d4e0c0fbda
  languageName: node
  linkType: hard

"abab@npm:^2.0.6":
  version: 2.0.6
  resolution: "abab@npm:2.0.6"
  checksum: 10c0/0b245c3c3ea2598fe0025abf7cc7bb507b06949d51e8edae5d12c1b847a0a0c09639abcb94788332b4e2044ac4491c1e8f571b51c7826fd4b0bda1685ad4a278
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"accepts@npm:^1.3.7, accepts@npm:^1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-globals@npm:^7.0.0":
  version: 7.0.1
  resolution: "acorn-globals@npm:7.0.1"
  dependencies:
    acorn: "npm:^8.1.0"
    acorn-walk: "npm:^8.0.2"
  checksum: 10c0/7437f58e92d99292dbebd0e79531af27d706c9f272f31c675d793da6c82d897e75302a8744af13c7f7978a8399840f14a353b60cf21014647f71012982456d2b
  languageName: node
  linkType: hard

"acorn-loose@npm:^8.3.0":
  version: 8.5.0
  resolution: "acorn-loose@npm:8.5.0"
  dependencies:
    acorn: "npm:^8.14.0"
  checksum: 10c0/221df4224ccad267d46775323d956636dd0e4ef263d18450258daff1a33d99e8e00e6214465bdc658fc961cc77fc60cef2f0f4710ec93543ce179f287da0ea80
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.2":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: "npm:^8.11.0"
  checksum: 10c0/76537ac5fb2c37a64560feaf3342023dadc086c46da57da363e64c6148dc21b57d49ace26f949e225063acb6fb441eabffd89f7a3066de5ad37ab3e328927c62
  languageName: node
  linkType: hard

"acorn@npm:^8.1.0, acorn@npm:^8.11.0, acorn@npm:^8.14.0, acorn@npm:^8.8.1":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dbd36c1ed1d2fa3550140000371fcf721578095b18777b85a79df231ca093b08edc6858d75d6e48c73e431c174dcf9214edbd7e6fa5911b93bd8abfa54e47123
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10c0/dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: "npm:^8.0.0"
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 10c0/e43ba22e91b6a48d96224b83d260d3a3a561b42d391f8d3c6d2c1559f9aa5b253bfb306bc94bbeca1d967c014e15a6efe9a207309e95b3eaae07fcbcdc2af662
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
  peerDependencies:
    ajv: ^8.8.2
  checksum: 10c0/18bec51f0171b83123ba1d8883c126e60c6f420cef885250898bf77a8d3e65e3bfb9e8564f497e30bdbe762a83e0d144a36931328616a973ee669dc74d4a9590
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.9.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10c0/ec3ba10a573c6b60f94639ffc53526275917a2df6810e4ab5a6b959d87459f9ef3f00d5e7865b82677cb7d21590355b34da14d1d0b9c32d75f95a187e76fff35
  languageName: node
  linkType: hard

"anser@npm:^1.4.9":
  version: 1.4.10
  resolution: "anser@npm:1.4.10"
  checksum: 10c0/ab251c96f6b9b8858e346137b75968ef3d287e10f358cd3981666949093e587defb5f7059a05a929eb44e1b3775bae346a55ab952e74049355e70f81b8b1ef53
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1, ansi-escapes@npm:^4.3.0":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-escapes@npm:^6.0.0":
  version: 6.2.1
  resolution: "ansi-escapes@npm:6.2.1"
  checksum: 10c0/a2c6f58b044be5f69662ee17073229b492daa2425a7fd99a665db6c22eab6e4ab42752807def7281c1c7acfed48f87f2362dda892f08c2c437f1b39c6b033103
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.1.0":
  version: 4.1.1
  resolution: "ansi-regex@npm:4.1.1"
  checksum: 10c0/d36d34234d077e8770169d980fed7b2f3724bfa2a01da150ccd75ef9707c80e883d27cdf7a0eac2f145ac1d10a785a8a855cffd05b85f778629a0db62e7033da
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.0, ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10c0/ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 10c0/60f0298ed34c74fef50daab88e8dab786036ed5a7fad02e012ab57e376e0a0b4b29e83b95ea9b5e7d89df762f5f25119b83e00706ecaccb22cfbacee98d74889
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3, anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"arg@npm:^5.0.2":
  version: 5.0.2
  resolution: "arg@npm:5.0.2"
  checksum: 10c0/ccaf86f4e05d342af6666c569f844bec426595c567d32a8289715087825c2ca7edd8a3d204e4d2fb2aa4602e09a57d0c13ea8c9eea75aac3dbb4af5514e6800e
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"array-timsort@npm:^1.0.3":
  version: 1.0.3
  resolution: "array-timsort@npm:1.0.3"
  checksum: 10c0/bd3a1707b621947265c89867e67c9102b9b9f4c50f5b3974220112290d8b60d26ce60595edec5deed3325207b759d70b758bed3cd310b5ddadb835657ffb6d12
  languageName: node
  linkType: hard

"asap@npm:~2.0.3, asap@npm:~2.0.6":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10c0/c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"ast-types@npm:^0.16.1":
  version: 0.16.1
  resolution: "ast-types@npm:0.16.1"
  dependencies:
    tslib: "npm:^2.0.1"
  checksum: 10c0/abcc49e42eb921a7ebc013d5bec1154651fb6dbc3f497541d488859e681256901b2990b954d530ba0da4d0851271d484f7057d5eff5e07cb73e8b10909f711bf
  languageName: node
  linkType: hard

"async-limiter@npm:~1.0.0":
  version: 1.0.1
  resolution: "async-limiter@npm:1.0.1"
  checksum: 10c0/0693d378cfe86842a70d4c849595a0bb50dc44c11649640ca982fa90cbfc74e3cc4753b5a0847e51933f2e9c65ce8e05576e75e5e1fd963a086e673735b35969
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 10c0/4c058baf6df1bc5a1697cf182e2029c58cd99975288a13f9e70068ef5d6f4e1f1fd7c4d2c3c4912eae44797d1725be9700995736deca441b39f3e66d8dee97ef
  languageName: node
  linkType: hard

"babel-core@npm:^7.0.0-bridge.0":
  version: 7.0.0-bridge.0
  resolution: "babel-core@npm:7.0.0-bridge.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f57576e30267be4607d163b7288031d332cf9200ea35efe9fb33c97f834e304376774c28c1f9d6928d6733fcde7041e4010f1248a0519e7730c590d4b07b9608
  languageName: node
  linkType: hard

"babel-jest@npm:^29.2.1, babel-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "babel-jest@npm:29.7.0"
  dependencies:
    "@jest/transform": "npm:^29.7.0"
    "@types/babel__core": "npm:^7.1.14"
    babel-plugin-istanbul: "npm:^6.1.1"
    babel-preset-jest: "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    slash: "npm:^3.0.0"
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: 10c0/2eda9c1391e51936ca573dd1aedfee07b14c59b33dbe16ef347873ddd777bcf6e2fc739681e9e9661ab54ef84a3109a03725be2ac32cd2124c07ea4401cbe8c1
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@istanbuljs/load-nyc-config": "npm:^1.0.0"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-instrument: "npm:^5.0.4"
    test-exclude: "npm:^6.0.0"
  checksum: 10c0/1075657feb705e00fd9463b329921856d3775d9867c5054b449317d39153f8fbcebd3e02ebf00432824e647faff3683a9ca0a941325ef1afe9b3c4dd51b24beb
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-plugin-jest-hoist@npm:29.6.3"
  dependencies:
    "@babel/template": "npm:^7.3.3"
    "@babel/types": "npm:^7.3.3"
    "@types/babel__core": "npm:^7.1.14"
    "@types/babel__traverse": "npm:^7.0.6"
  checksum: 10c0/7e6451caaf7dce33d010b8aafb970e62f1b0c0b57f4978c37b0d457bbcf0874d75a395a102daf0bae0bd14eafb9f6e9a165ee5e899c0a4f1f3bb2e07b304ed2e
  languageName: node
  linkType: hard

"babel-plugin-module-resolver@npm:^5.0.2":
  version: 5.0.2
  resolution: "babel-plugin-module-resolver@npm:5.0.2"
  dependencies:
    find-babel-config: "npm:^2.1.1"
    glob: "npm:^9.3.3"
    pkg-up: "npm:^3.1.0"
    reselect: "npm:^4.1.7"
    resolve: "npm:^1.22.8"
  checksum: 10c0/ccbb9e673c4219f68937349267521becb72be292cf30bf70b861c3e709d24fbfa589da0bf6c100a0def799d38199299171cb6eac3fb00b1ea740373e2c1fe54c
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.13
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.13"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b4a54561606d388e6f9499f39f03171af4be7f9ce2355e737135e40afa7086cf6790fdd706c2e59f488c8fa1f76123d28783708e07ddc84647dca8ed8fb98e06
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.11.0":
  version: 0.11.1
  resolution: "babel-plugin-polyfill-corejs3@npm:0.11.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.3"
    core-js-compat: "npm:^3.40.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/025f754b6296d84b20200aff63a3c1acdd85e8c621781f2bd27fe2512d0060526192d02329326947c6b29c27cf475fbcfaaff8c51eab1d2bfc7b79086bb64229
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.4
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/ebaaf9e4e53201c02f496d3f686d815e94177b3e55b35f11223b99c60d197a29f907a2e87bbcccced8b7aff22a807fccc1adaf04722864a8e1862c8845ab830a
  languageName: node
  linkType: hard

"babel-plugin-react-native-web@npm:~0.19.13":
  version: 0.19.13
  resolution: "babel-plugin-react-native-web@npm:0.19.13"
  checksum: 10c0/0710db342063182163d58febfb01ef510c9460f0500f9faaf47603d06dda37554f216e6123a099a343eb2067c2dfb43c9d4ca573a9d659662ca429048db11af4
  languageName: node
  linkType: hard

"babel-plugin-syntax-hermes-parser@npm:0.25.1, babel-plugin-syntax-hermes-parser@npm:^0.25.1":
  version: 0.25.1
  resolution: "babel-plugin-syntax-hermes-parser@npm:0.25.1"
  dependencies:
    hermes-parser: "npm:0.25.1"
  checksum: 10c0/8f4a0cb65056162b2d4c64d0ccd4d2fdeac8218e83e0338e92564ead659fd9b9351277ed2a10e958d0d8dc4c60591d5b1a40aa425bf0cbf67224e9767c557abf
  languageName: node
  linkType: hard

"babel-plugin-transform-flow-enums@npm:^0.0.2":
  version: 0.0.2
  resolution: "babel-plugin-transform-flow-enums@npm:0.0.2"
  dependencies:
    "@babel/plugin-syntax-flow": "npm:^7.12.1"
  checksum: 10c0/aa9d022d8d4be0e7c4f1ff7e5308fe7e0ff4d6f9099449913e3a11c1e81916623a8f36432da180a9aa3f53ea534dca4401fe33d6528f043f40357cfa790ee778
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.1.0
  resolution: "babel-preset-current-node-syntax@npm:1.1.0"
  dependencies:
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
    "@babel/plugin-syntax-bigint": "npm:^7.8.3"
    "@babel/plugin-syntax-class-properties": "npm:^7.12.13"
    "@babel/plugin-syntax-class-static-block": "npm:^7.14.5"
    "@babel/plugin-syntax-import-attributes": "npm:^7.24.7"
    "@babel/plugin-syntax-import-meta": "npm:^7.10.4"
    "@babel/plugin-syntax-json-strings": "npm:^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators": "npm:^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
    "@babel/plugin-syntax-numeric-separator": "npm:^7.10.4"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
    "@babel/plugin-syntax-private-property-in-object": "npm:^7.14.5"
    "@babel/plugin-syntax-top-level-await": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/0b838d4412e3322cb4436f246e24e9c00bebcedfd8f00a2f51489db683bd35406bbd55a700759c28d26959c6e03f84dd6a1426f576f440267c1d7a73c5717281
  languageName: node
  linkType: hard

"babel-preset-expo@npm:~13.1.11":
  version: 13.1.11
  resolution: "babel-preset-expo@npm:13.1.11"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/plugin-proposal-decorators": "npm:^7.12.9"
    "@babel/plugin-proposal-export-default-from": "npm:^7.24.7"
    "@babel/plugin-syntax-export-default-from": "npm:^7.24.7"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.25.9"
    "@babel/plugin-transform-flow-strip-types": "npm:^7.25.2"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.24.8"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.24.7"
    "@babel/plugin-transform-parameters": "npm:^7.24.7"
    "@babel/plugin-transform-private-methods": "npm:^7.24.7"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.24.7"
    "@babel/plugin-transform-runtime": "npm:^7.24.7"
    "@babel/preset-react": "npm:^7.22.15"
    "@babel/preset-typescript": "npm:^7.23.0"
    "@react-native/babel-preset": "npm:0.79.2"
    babel-plugin-react-native-web: "npm:~0.19.13"
    babel-plugin-syntax-hermes-parser: "npm:^0.25.1"
    babel-plugin-transform-flow-enums: "npm:^0.0.2"
    debug: "npm:^4.3.4"
    react-refresh: "npm:^0.14.2"
    resolve-from: "npm:^5.0.0"
  peerDependencies:
    babel-plugin-react-compiler: ^19.0.0-beta-e993439-20250405
  peerDependenciesMeta:
    babel-plugin-react-compiler:
      optional: true
  checksum: 10c0/ae46e715c46299e921973d1ef5210415a0ac05cc4ff8885abbdbc7e692a9fb0e34cdd599ae6ff2eee7c48284f5e4aebad896087d416dd69bff0094e9561fadde
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-preset-jest@npm:29.6.3"
  dependencies:
    babel-plugin-jest-hoist: "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/ec5fd0276b5630b05f0c14bb97cc3815c6b31600c683ebb51372e54dcb776cff790bdeeabd5b8d01ede375a040337ccbf6a3ccd68d3a34219125945e167ad943
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base-64@npm:^1.0.0":
  version: 1.0.0
  resolution: "base-64@npm:1.0.0"
  checksum: 10c0/d886cb3236cee0bed9f7075675748b59b32fad623ddb8ce1793c790306aa0f76a03238cad4b3fb398abda6527ce08a5588388533a4ccade0b97e82b9da660e28
  languageName: node
  linkType: hard

"base64-arraybuffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "base64-arraybuffer@npm:1.0.2"
  checksum: 10c0/3acac95c70f9406e87a41073558ba85b6be9dbffb013a3d2a710e3f2d534d506c911847d5d9be4de458af6362c676de0a5c4c2d7bdf4def502d00b313368e72f
  languageName: node
  linkType: hard

"base64-js@npm:^1.2.3, base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"better-opn@npm:~3.0.2":
  version: 3.0.2
  resolution: "better-opn@npm:3.0.2"
  dependencies:
    open: "npm:^8.0.4"
  checksum: 10c0/911ef25d44da75aabfd2444ce7a4294a8000ebcac73068c04a60298b0f7c7506b60421aa4cd02ac82502fb42baaff7e4892234b51e6923eded44c5a11185f2f5
  languageName: node
  linkType: hard

"big-integer@npm:1.6.x":
  version: 1.6.52
  resolution: "big-integer@npm:1.6.52"
  checksum: 10c0/9604224b4c2ab3c43c075d92da15863077a9f59e5d4205f4e7e76acd0cd47e8d469ec5e5dba8d9b32aa233951893b29329ca56ac80c20ce094b4a647a66abae0
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10c0/e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 10c0/04efeecc7927a9ec33c667fa0965dea19f4ac60b3fea60793c2e6cf06c1dcd2f7ae1dbc656f450c5f50783b1c75cf9dc173ba6f3b7db2feee01f8c4b793e1bd3
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.0":
  version: 0.1.0
  resolution: "bplist-creator@npm:0.1.0"
  dependencies:
    stream-buffers: "npm:2.2.x"
  checksum: 10c0/86f5fe95f34abd369b381abf0f726e220ecebd60a3d932568ae94895ccf1989a87553e4aee9ab3cfb4f35e6f72319f52aa73028165eec82819ed39f15189d493
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.1":
  version: 0.1.1
  resolution: "bplist-creator@npm:0.1.1"
  dependencies:
    stream-buffers: "npm:2.2.x"
  checksum: 10c0/427ec37263ce0e8c68a83f595fc9889a9cbf2e6fda2de18e1f8ef7f0c6ce68c0cdbb7c9c1f3bb3f2d217407af8cffbdf254bf0f71c99f2186175d07752f08a47
  languageName: node
  linkType: hard

"bplist-parser@npm:0.3.2, bplist-parser@npm:^0.3.1":
  version: 0.3.2
  resolution: "bplist-parser@npm:0.3.2"
  dependencies:
    big-integer: "npm:1.6.x"
  checksum: 10c0/4dc307c11d2511a01255e87e370d4ab6f1962b35fdc27605fd4ce9a557a259c2dc9f87822617ddb1f7aa062a71e30ef20d6103329ac7ce235628f637fb0ed763
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.24.4":
  version: 4.24.5
  resolution: "browserslist@npm:4.24.5"
  dependencies:
    caniuse-lite: "npm:^1.0.30001716"
    electron-to-chromium: "npm:^1.5.149"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/f4c1ce1a7d8fdfab5e5b88bb6e93d09e8a883c393f86801537a252da0362dbdcde4dbd97b318246c5d84c6607b2f6b47af732c1b000d6a8a881ee024bad29204
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: "npm:^0.4.0"
  checksum: 10c0/24d8dfb7b6d457d73f32744e678a60cc553e4ec0e9e1a01cf614b44d85c3c87e188d3cc78ef0442ce5032ee6818de20a0162ba1074725c0d08908f62ea979227
  languageName: node
  linkType: hard

"bson@npm:^4.7.2":
  version: 4.7.2
  resolution: "bson@npm:4.7.2"
  dependencies:
    buffer: "npm:^5.6.0"
  checksum: 10c0/ce97286239f1cc8bbc61214cee5f6a64a306a1a0c6b529b80993205738cd0287931cd09a396213ea358f886bbad6b304ca8faf8519ea73726eb467f1750f3d6f
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:^5.4.3, buffer@npm:^5.6.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10c0/27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10c0/a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"caller-callsite@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-callsite@npm:2.0.0"
  dependencies:
    callsites: "npm:^2.0.0"
  checksum: 10c0/a00ca91280e10ee2321de21dda6c168e427df7a63aeaca027ea45e3e466ac5e1a5054199f6547ba1d5a513d3b6b5933457266daaa47f8857fb532a343ee6b5e1
  languageName: node
  linkType: hard

"caller-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-path@npm:2.0.0"
  dependencies:
    caller-callsite: "npm:^2.0.0"
  checksum: 10c0/029b5b2c557d831216305c3218e9ff30fa668be31d58dd08088f74c8eabc8362c303e0908b3a93abb25ba10e3a5bfc9cff5eb7fab6ab9cf820e3b160ccb67581
  languageName: node
  linkType: hard

"callsites@npm:^2.0.0":
  version: 2.0.0
  resolution: "callsites@npm:2.0.0"
  checksum: 10c0/13bff4fee946e6020b37e76284e95e24aa239c9e34ac4f3451e4c5330fca6f2f962e1d1ab69e4da7940e1fce135107a2b2b98c01d62ea33144350fc89dc5494e
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase-css@npm:^2.0.1":
  version: 2.0.1
  resolution: "camelcase-css@npm:2.0.1"
  checksum: 10c0/1a1a3137e8a781e6cbeaeab75634c60ffd8e27850de410c162cce222ea331cd1ba5364e8fb21c95e5ca76f52ac34b81a090925ca00a87221355746d049c6e273
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10c0/92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10c0/0d701658219bd3116d12da3eab31acddb3f9440790c0792e0d398f0a520a6a4058018e546862b6fba89d7ae990efaeb97da71e1913e9ebf5a8b5621a3d55c710
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.1
  resolution: "camelize@npm:1.0.1"
  checksum: 10c0/4c9ac55efd356d37ac483bad3093758236ab686192751d1c9daa43188cc5a07b09bd431eb7458a4efd9ca22424bba23253e7b353feb35d7c749ba040de2385fb
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001716":
  version: 1.0.30001718
  resolution: "caniuse-lite@npm:1.0.30001718"
  checksum: 10c0/67f9ad09bc16443e28d14f265d6e468480cd8dc1900d0d8b982222de80c699c4f2306599c3da8a3fa7139f110d4b30d49dbac78f215470f479abb6ffe141d5d3
  languageName: node
  linkType: hard

"canvaskit-wasm@npm:0.40.0":
  version: 0.40.0
  resolution: "canvaskit-wasm@npm:0.40.0"
  dependencies:
    "@webgpu/types": "npm:0.1.21"
  checksum: 10c0/dde817c0ef979a67afad4ac3adf31c03133af2592efec17f715a98508fdf801104fbf05af29723fb39b4d8b2798ce5306bb3cb5c062209d9d90a750430f3361d
  languageName: node
  linkType: hard

"chalk@npm:^2.0.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 10c0/e6543f02ec877732e3a2d1c3c3323ddb4d39fbab687c23f526e25bd4c6a9bf3b83a696e8c769d078e04e5754921648f7821b2a2acfd16c550435fd630026e073
  languageName: node
  linkType: hard

"chalk@npm:^3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/ee650b0a065b3d7a6fda258e75d3a86fc8e4effa55871da730a9e42ccb035bf5fd203525e5a1ef45ec2582ecc4f65b47eb11357c526b84dd29a14fb162c414d2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: 10c0/57a09a86371331e0be35d9083ba429e86c4f4648ecbe27455dbfb343037c16ee6fdc7f6b61f433a57cc5ded5561d71c56a150e018f40c2ffb7bc93a26dae341e
  languageName: node
  linkType: hard

"char-regex@npm:^2.0.0":
  version: 2.0.2
  resolution: "char-regex@npm:2.0.2"
  checksum: 10c0/afbfb11019bafcc70a3e85b760d63336cf941f7608f1df7d746a60ee6075d1926e5c18a9fb1b6c22024f3a000c0e0c745f059b2bf679a5cba6cb48adf7ea43ce
  languageName: node
  linkType: hard

"chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"chrome-launcher@npm:^0.15.2":
  version: 0.15.2
  resolution: "chrome-launcher@npm:0.15.2"
  dependencies:
    "@types/node": "npm:*"
    escape-string-regexp: "npm:^4.0.0"
    is-wsl: "npm:^2.2.0"
    lighthouse-logger: "npm:^1.0.0"
  bin:
    print-chrome-path: bin/print-chrome-path.js
  checksum: 10c0/fc01abc19af753bb089744362c0de48707f32ea15779407b06fb569e029a6b1fbaa78107165539d768915cf54b5c38594e73d95563c34127873e3826fb43c636
  languageName: node
  linkType: hard

"chromium-edge-launcher@npm:^0.2.0":
  version: 0.2.0
  resolution: "chromium-edge-launcher@npm:0.2.0"
  dependencies:
    "@types/node": "npm:*"
    escape-string-regexp: "npm:^4.0.0"
    is-wsl: "npm:^2.2.0"
    lighthouse-logger: "npm:^1.0.0"
    mkdirp: "npm:^1.0.4"
    rimraf: "npm:^3.0.2"
  checksum: 10c0/880972816dd9b95c0eb77d1f707569667a8cce7cc29fe9c8d199c47fdfbe4971e9da3e5a29f61c4ecec29437ac7cebbbb5afc30bec96306579d1121e7340606a
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 10c0/8c5fa3830a2bcee2b53c2e5018226f0141db9ec9f7b1e27a5c57db5512332cde8a0beb769bcbaf0d8775a78afbf2bb841928feca4ea6219638a5b088f9884b46
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0, ci-info@npm:^3.3.0, ci-info@npm:^3.7.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 10c0/6f0109e36e111684291d46123d491bc4e7b7a1934c3a20dea28cba89f1d4a03acd892f5f6a81ed3855c38647e285a150e3c9ba062e38943bef57fee6c1554c3a
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0":
  version: 1.4.3
  resolution: "cjs-module-lexer@npm:1.4.3"
  checksum: 10c0/076b3af85adc4d65dbdab1b5b240fe5b45d44fcf0ef9d429044dd94d19be5589376805c44fb2d4b3e684e5fe6a9b7cf3e426476a6507c45283c5fc6ff95240be
  languageName: node
  linkType: hard

"cli-cursor@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-cursor@npm:2.1.0"
  dependencies:
    restore-cursor: "npm:^2.0.0"
  checksum: 10c0/09ee6d8b5b818d840bf80ec9561eaf696672197d3a02a7daee2def96d5f52ce6e0bbe7afca754ccf14f04830b5a1b4556273e983507d5029f95bba3016618eda
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.0.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 10c0/907a1c227ddf0d7a101e7ab8b300affc742ead4b4ebe920a5bf1bc6d45dce2958fcd195eb28fa25275062fe6fa9b109b93b63bc8033396ed3bcb50297008b3a3
  languageName: node
  linkType: hard

"client-only@npm:^0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 10c0/9d6cfd0c19e1c96a434605added99dff48482152af791ec4172fb912a71cff9027ff174efd8cdb2160cc7f377543e0537ffc462d4f279bc4701de3f2a3c4b358
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
    kind-of: "npm:^6.0.2"
    shallow-clone: "npm:^3.0.0"
  checksum: 10c0/637753615aa24adf0f2d505947a1bb75e63964309034a1cf56ba4b1f30af155201edd38d26ffe26911adaae267a3c138b344a4947d39f5fc1b6d6108125aa758
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 10c0/2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0, clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10c0/c4c8eb865f8c82baab07e71bfa8897c73454881c4f99d6bc81585aecd7c441746c1399d08363dc096c550cceaf97bd4ce1e8854e1771e9998d9f94c4fe075839
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 10c0/c0e85ea0ca8bf0a50cbdca82efc5af0301240ca88ebe3644a6ffb8ffe911f34d40f8fbcf8f1d52c5ddd66706abd4d3bfcd64259f1e8e2371d4f47573b0dc8c28
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: 10c0/ed7008e2e8b6852c5483b444a3ae6e976e088d4335a85aa0a9db2861c5f1d31bd2d7ff97a60469b3388deeba661a619753afbe201279fb159b4b9548ab8269a1
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0, color-convert@npm:^1.9.3":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10c0/5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10c0/566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.6.0, color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color@npm:^3.1.2":
  version: 3.2.1
  resolution: "color@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.3"
    color-string: "npm:^1.6.0"
  checksum: 10c0/39345d55825884c32a88b95127d417a2c24681d8b57069413596d9fcbb721459ef9d9ec24ce3e65527b5373ce171b73e38dbcd9c830a52a6487e7f37bf00e83c
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10c0/7fbe7cfb811054c808349de19fb380252e5e34e61d7d168ec3353e9e9aacb1802674bddc657682e4e9730c2786592a4de6f8283e7e0d3870b829bb0b7b2f6118
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:^12.0.0":
  version: 12.1.0
  resolution: "commander@npm:12.1.0"
  checksum: 10c0/6e1996680c083b3b897bfc1cfe1c58dfbcd9842fd43e1aaf8a795fbc237f65efcc860a3ef457b318e73f29a4f4a28f6403c3d653d021d960e4632dd45bde54a9
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"commander@npm:^4.0.0":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: 10c0/84a76c08fe6cc08c9c93f62ac573d2907d8e79138999312c92d4155bc2325d487d64d13f669b2000c9f8caf70493c1be2dac74fec3c51d5a04f8bc3ae1830bab
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 10c0/8d690ff13b0356df7e0ebbe6c59b4712f754f4b724d4f473d3cc5b3fdcf978e3a5dc3078717858a2ceb50b0f84d0660a7f22a96cdc50fb877d0c9bb31593d23a
  languageName: node
  linkType: hard

"comment-json@npm:^4.2.5":
  version: 4.2.5
  resolution: "comment-json@npm:4.2.5"
  dependencies:
    array-timsort: "npm:^1.0.3"
    core-util-is: "npm:^1.0.3"
    esprima: "npm:^4.0.1"
    has-own-prop: "npm:^2.0.0"
    repeat-string: "npm:^1.6.1"
  checksum: 10c0/e22f13f18fcc484ac33c8bc02a3d69c3f9467ae5063fdfb3df7735f83a8d9a2cab6a32b7d4a0c53123413a9577de8e17c8cc88369c433326799558febb34ef9c
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 10c0/33a124960e471c25ee19280c9ce31ccc19574b566dc514fe4f4ca4c34fa8b0b57cf437671f5de380e11353ea9426213fca17687dd2ef03134fea2dbc53809fd6
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 10c0/8a03712bc9f5b9fe530cc5a79e164e665550d5171a64575d7dcf3e0395d7b4afa2d79ab176c61b5b596e28228b350dd07c1a2a6ead12fd81d1b6cd632af2fef7
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.8.0
  resolution: "compression@npm:1.8.0"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.0.2"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/804d3c8430939f4fd88e5128333f311b4035f6425a7f2959d74cfb5c98ef3a3e3e18143208f3f9d0fcae4cd3bcf3d2fbe525e0fcb955e6e146e070936f025a24
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"connect@npm:^3.6.5, connect@npm:^3.7.0":
  version: 3.7.0
  resolution: "connect@npm:3.7.0"
  dependencies:
    debug: "npm:2.6.9"
    finalhandler: "npm:1.1.2"
    parseurl: "npm:~1.3.3"
    utils-merge: "npm:1.0.1"
  checksum: 10c0/f120c6116bb16a0a7d2703c0b4a0cd7ed787dc5ec91978097bf62aa967289020a9f41a9cd3c3276a7b92aaa36f382d2cd35fed7138fd466a55c8e9fdbed11ca8
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.40.0":
  version: 3.42.0
  resolution: "core-js-compat@npm:3.42.0"
  dependencies:
    browserslist: "npm:^4.24.4"
  checksum: 10c0/0138ce005c13ce642fc38e18e54a52a1c78ca8315ee6e4faad748d2a1b0ad2462ea615285ad4e6cf77afe48e47a868d898e64c70606c1eb1c9e6a9f19ee2b186
  languageName: node
  linkType: hard

"core-util-is@npm:^1.0.3":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cosmiconfig@npm:^5.0.5":
  version: 5.2.1
  resolution: "cosmiconfig@npm:5.2.1"
  dependencies:
    import-fresh: "npm:^2.0.0"
    is-directory: "npm:^0.3.1"
    js-yaml: "npm:^3.13.1"
    parse-json: "npm:^4.0.0"
  checksum: 10c0/ae9ba309cdbb42d0c9d63dad5c1dfa1c56bb8f818cb8633eea14fd2dbdc9f33393b77658ba96fdabda497bc943afed8c3371d1222afe613c518ba676fa624645
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.1.3":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: "npm:^3.3.0"
    js-yaml: "npm:^4.1.0"
    parse-json: "npm:^5.2.0"
    path-type: "npm:^4.0.0"
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/0382a9ed13208f8bfc22ca2f62b364855207dffdb73dc26e150ade78c3093f1cf56172df2dd460c8caf2afa91c0ed4ec8a88c62f8f9cd1cf423d26506aa8797a
  languageName: node
  linkType: hard

"create-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "create-jest@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    exit: "npm:^0.1.2"
    graceful-fs: "npm:^4.2.9"
    jest-config: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    prompts: "npm:^2.0.1"
  bin:
    create-jest: bin/create-jest.js
  checksum: 10c0/e7e54c280692470d3398f62a6238fd396327e01c6a0757002833f06d00afc62dd7bfe04ff2b9cd145264460e6b4d1eb8386f2925b7e567f97939843b7b0e812f
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.5":
  version: 3.2.0
  resolution: "cross-fetch@npm:3.2.0"
  dependencies:
    node-fetch: "npm:^2.7.0"
  checksum: 10c0/d8596adf0269130098a676f6739a0922f3cc7b71cc89729925411ebe851a87026171c82ea89154c4811c9867c01c44793205a52e618ce2684650218c7fbeeb9f
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"crypto-random-string@npm:^2.0.0":
  version: 2.0.0
  resolution: "crypto-random-string@npm:2.0.0"
  checksum: 10c0/288589b2484fe787f9e146f56c4be90b940018f17af1b152e4dde12309042ff5a2bf69e949aab8b8ac253948381529cc6f3e5a2427b73643a71ff177fa122b37
  languageName: node
  linkType: hard

"css-color-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-color-keywords@npm:1.0.0"
  checksum: 10c0/af205a86c68e0051846ed91eb3e30b4517e1904aac040013ff1d742019b3f9369ba5658ba40901dbbc121186fc4bf0e75a814321cc3e3182fbb2feb81c6d9cb7
  languageName: node
  linkType: hard

"css-in-js-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "css-in-js-utils@npm:3.1.0"
  dependencies:
    hyphenate-style-name: "npm:^1.0.3"
  checksum: 10c0/8bb042e8f7701a7edadc3cce5ce2d5cf41189631d7e2aed194d5a7059b25776dded2a0466cb9da1d1f3fc6c99dcecb51e45671148d073b8a2a71e34755152e52
  languageName: node
  linkType: hard

"css-line-break@npm:^2.1.0":
  version: 2.1.0
  resolution: "css-line-break@npm:2.1.0"
  dependencies:
    utrie: "npm:^1.0.2"
  checksum: 10c0/b2222d99d5daf7861ecddc050244fdce296fad74b000dcff6bdfb1eb16dc2ef0b9ffe2c1c965e3239bd05ebe9eadb6d5438a91592fa8648d27a338e827cf9048
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: 10c0/551c60dba5b54054741032c1793b5734f6ba45e23ae9e82761a3c0ed1acbb8cfedfa443aaba3a3c1a54cac12b456d2012a09d2cd5f0e82e430454c1b9d84d500
  languageName: node
  linkType: hard

"css-to-react-native@npm:3.2.0":
  version: 3.2.0
  resolution: "css-to-react-native@npm:3.2.0"
  dependencies:
    camelize: "npm:^1.0.0"
    css-color-keywords: "npm:^1.0.0"
    postcss-value-parser: "npm:^4.0.2"
  checksum: 10c0/fde850a511d5d3d7c55a1e9b8ed26b69a8ad4868b3487e36ebfbfc0b96fc34bc977d9cd1d61a289d0c74d3f9a662d8cee297da53d4433bf2e27d6acdff8e1003
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.3":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: "npm:2.0.14"
    source-map: "npm:^0.6.1"
  checksum: 10c0/499a507bfa39b8b2128f49736882c0dd636b0cd3370f2c69f4558ec86d269113286b7df469afc955de6a68b0dba00bc533e40022a73698081d600072d5d83c1c
  languageName: node
  linkType: hard

"css-tree@npm:^2.3.1":
  version: 2.3.1
  resolution: "css-tree@npm:2.3.1"
  dependencies:
    mdn-data: "npm:2.0.30"
    source-map-js: "npm:^1.0.1"
  checksum: 10c0/6f8c1a11d5e9b14bf02d10717fc0351b66ba12594166f65abfbd8eb8b5b490dd367f5c7721db241a3c792d935fc6751fbc09f7e1598d421477ad9fadc30f4f24
  languageName: node
  linkType: hard

"css-tree@npm:~2.2.0":
  version: 2.2.1
  resolution: "css-tree@npm:2.2.1"
  dependencies:
    mdn-data: "npm:2.0.28"
    source-map-js: "npm:^1.0.1"
  checksum: 10c0/47e87b0f02f8ac22f57eceb65c58011dd142d2158128882a0bf963cf2eabb81a4ebbc2e3790c8289be7919fa8b83750c7b69272bd66772c708143b772ba3c186
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: 10c0/a09f5a6b14ba8dcf57ae9a59474722e80f20406c53a61e9aedb0eedc693b135113ffe2983f4efc4b5065ae639442e9ae88df24941ef159c218b231011d733746
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10c0/6bcfd898662671be15ae7827120472c5667afb3d7429f1f917737f3bf84c4176003228131b643ae74543f17a394446247df090c597bb9a728cce298606ed0aa7
  languageName: node
  linkType: hard

"csso@npm:^5.0.5":
  version: 5.0.5
  resolution: "csso@npm:5.0.5"
  dependencies:
    css-tree: "npm:~2.2.0"
  checksum: 10c0/ab4beb1e97dd7e207c10e9925405b45f15a6cd1b4880a8686ad573aa6d476aed28b4121a666cffd26c37a26179f7b54741f7c257543003bfb244d06a62ad569b
  languageName: node
  linkType: hard

"cssom@npm:^0.5.0":
  version: 0.5.0
  resolution: "cssom@npm:0.5.0"
  checksum: 10c0/8c4121c243baf0678c65dcac29b201ff0067dfecf978de9d5c83b2ff127a8fdefd2bfd54577f5ad8c80ed7d2c8b489ae01c82023545d010c4ecb87683fb403dd
  languageName: node
  linkType: hard

"cssom@npm:~0.3.6":
  version: 0.3.8
  resolution: "cssom@npm:0.3.8"
  checksum: 10c0/d74017b209440822f9e24d8782d6d2e808a8fdd58fa626a783337222fe1c87a518ba944d4c88499031b4786e68772c99dfae616638d71906fe9f203aeaf14411
  languageName: node
  linkType: hard

"cssstyle@npm:^2.3.0":
  version: 2.3.0
  resolution: "cssstyle@npm:2.3.0"
  dependencies:
    cssom: "npm:~0.3.6"
  checksum: 10c0/863400da2a458f73272b9a55ba7ff05de40d850f22eb4f37311abebd7eff801cf1cd2fb04c4c92b8c3daed83fe766e52e4112afb7bc88d86c63a9c2256a7d178
  languageName: node
  linkType: hard

"csstype@npm:3.1.3, csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"data-urls@npm:^3.0.2":
  version: 3.0.2
  resolution: "data-urls@npm:3.0.2"
  dependencies:
    abab: "npm:^2.0.6"
    whatwg-mimetype: "npm:^3.0.0"
    whatwg-url: "npm:^11.0.0"
  checksum: 10c0/051c3aaaf3e961904f136aab095fcf6dff4db23a7fc759dd8ba7b3e6ba03fc07ef608086caad8ab910d864bd3b5e57d0d2f544725653d77c96a2c971567045f4
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10c0/a3caf6ac8363c7dade9d1ee797848ddcf25c1ace68d9fe8678ecf8ba0675825430de5d793672ec87b24a69bf04a1544b176547b2539982275d5542a7955f35b7
  languageName: node
  linkType: hard

"ddp-client@npm:^0.1.2":
  version: 0.1.2
  resolution: "ddp-client@npm:0.1.2"
  dependencies:
    ejson: "npm:2.0.1"
    events: "npm:1.0.2"
    minimongo-cache: "npm:0.0.25"
    underscore: "npm:1.8.3"
  checksum: 10c0/a2fecf3c1bccdcb869d0f2b10cd9bda3d5d28e67e815f047cb9490efb1c6ab062fe7afd2b702ba0e9b0a095971e526326aa34a7c6ad3b5c56dc3a9f360f63062
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.3.5, debug@npm:^4.3.7, debug@npm:^4.4.0":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"debug@npm:^3.1.0":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10c0/37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"decimal.js@npm:^10.4.2, decimal.js@npm:^10.4.3":
  version: 10.5.0
  resolution: "decimal.js@npm:10.5.0"
  checksum: 10c0/785c35279df32762143914668df35948920b6c1c259b933e0519a69b7003fc0a5ed2a766b1e1dda02574450c566b21738a45f15e274b47c2ac02072c0d1f3ac3
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 10c0/1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"dedent@npm:^1.0.0":
  version: 1.6.0
  resolution: "dedent@npm:1.6.0"
  peerDependencies:
    babel-plugin-macros: ^3.1.0
  peerDependenciesMeta:
    babel-plugin-macros:
      optional: true
  checksum: 10c0/671b8f5e390dd2a560862c4511dd6d2638e71911486f78cb32116551f8f2aa6fcaf50579ffffb2f866d46b5b80fd72470659ca5760ede8f967619ef7df79e8a5
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 10c0/1c6b0abcdb901e13a44c7d699116d3d4279fdb261983122a3783e7273844d5f2537dc2e1c454a23fcf645917f93fbf8d07101c1d03c015a87faa662755212566
  languageName: node
  linkType: hard

"deepmerge@npm:^3.2.0":
  version: 3.3.0
  resolution: "deepmerge@npm:3.3.0"
  checksum: 10c0/143bc6b6cd8a1216565c61c0fe38bf43fe691fb6876fb3f5727c6e323defe4e947c68fbab9957e17e837c5594a56af885c5834d23dc6cf2c41bef97090005104
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2, deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10c0/e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 10c0/9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10c0/db6c63864a9d3b7dc9def55d52764968a5af296de87c1b2cc71d8be8142e445208071953649e0386a8cc37cfcf9a2067a47207f1eb9ff250c2a269658fdae422
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10c0/4da0deae9f69e13bc37a0902d78bf7169480004b1fed3c19722d56cff578d16f0e11633b7fbf5fb6249181236c72e90024cbd68f0b9558ae06e281f47326d50d
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.3":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 10c0/c15541f836eba4b1f521e4eecc28eefefdbc10a94d3b8cb4c507689f332cc111babb95deda66f2de050b22122113189986d5190be97d51b5a2b23b938415e67c
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: 10c0/c38cfc8eeb9fda09febb44bcd85e467c970d4e3bf526095394e5a4f18bc26dd0cf6b22c69c1fa9969261521c593836db335c2795218f6d781a512aea2fb8209d
  languageName: node
  linkType: hard

"didyoumean@npm:^1.2.2":
  version: 1.2.2
  resolution: "didyoumean@npm:1.2.2"
  checksum: 10c0/95d0b53d23b851aacff56dfadb7ecfedce49da4232233baecfeecb7710248c4aa03f0aa8995062f0acafaf925adf8536bd7044a2e68316fd7d411477599bc27b
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.6.3":
  version: 29.6.3
  resolution: "diff-sequences@npm:29.6.3"
  checksum: 10c0/32e27ac7dbffdf2fb0eb5a84efd98a9ad084fbabd5ac9abb8757c6770d5320d2acd172830b28c4add29bb873d59420601dfc805ac4064330ce59b1adfd0593b2
  languageName: node
  linkType: hard

"dlv@npm:^1.1.3":
  version: 1.1.3
  resolution: "dlv@npm:1.1.3"
  checksum: 10c0/03eb4e769f19a027fd5b43b59e8a05e3fd2100ac239ebb0bf9a745de35d449e2f25cfaf3aa3934664551d72856f4ae8b7822016ce5c42c2d27c18ae79429ec42
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.0.0":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.8.7"
    csstype: "npm:^3.0.2"
  checksum: 10c0/f735074d66dd759b36b158fa26e9d00c9388ee0e8c9b16af941c38f014a37fc80782de83afefd621681b19ac0501034b4f1c4a3bff5caa1b8667f0212b5e124c
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10c0/d5ae2b7110ca3746b3643d3ef60ef823f5f078667baf530cec096433f1627ec4b6fa8c072f09d079d7cda915fd2c7bc1b7b935681e9b09e591e1e15f4040b8e2
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domexception@npm:^4.0.0":
  version: 4.0.0
  resolution: "domexception@npm:4.0.0"
  dependencies:
    webidl-conversions: "npm:^7.0.0"
  checksum: 10c0/774277cd9d4df033f852196e3c0077a34dbd15a96baa4d166e0e47138a80f4c0bdf0d94e4703e6ff5883cec56bb821a6fff84402d8a498e31de7c87eb932a294
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10c0/bba1e5932b3e196ad6862286d76adc89a0dbf0c773e5ced1eb01f9af930c50093a084eff14b8de5ea60b895c56a04d5de8bbc4930c5543d029091916770b2d2a
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10c0/47938f473b987ea71cd59e59626eb8666d3aa8feba5266e45527f3b636c7883cca7e582d901531961f742c519d7514636b7973353b648762b2e3bedbf235fada
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/5b859ea65097a7ea870e2c91b5768b72ddf7fa947223fd29e167bcdff58fe731d941c48e47a38ec8aa8e43044c8fbd15cd8fa21689a526bc34b6548197cd5b05
  languageName: node
  linkType: hard

"dotenv-expand@npm:~11.0.6":
  version: 11.0.7
  resolution: "dotenv-expand@npm:11.0.7"
  dependencies:
    dotenv: "npm:^16.4.5"
  checksum: 10c0/d80b8a7be085edf351270b96ac0e794bc3ddd7f36157912939577cb4d33ba6492ebee349d59798b71b90e36f498d24a2a564fb4aa00073b2ef4c2a3a49c467b1
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.5":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 10c0/5bc94c919fbd955bf0ba44d33922a1e93d1078e64a1db5c30faeded1d996e7a83c55332cb8ea4fae5a9ca4d0be44cbceb95c5811e70f9f095298df09d1997dd9
  languageName: node
  linkType: hard

"dotenv@npm:~16.4.5":
  version: 16.4.7
  resolution: "dotenv@npm:16.4.7"
  checksum: 10c0/be9f597e36a8daf834452daa1f4cc30e5375a5968f98f46d89b16b983c567398a330580c88395069a77473943c06b877d1ca25b4afafcdd6d4adb549e8293462
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"ejson@npm:2.0.1":
  version: 2.0.1
  resolution: "ejson@npm:2.0.1"
  dependencies:
    underscore: "npm:1.7.x"
  checksum: 10c0/ff9d0f61f25fc24bb6c566aab65560ac4fefa7e8e2c9e2c0cfdc3e63a60b761bc9416cfb75674fade57fc62c9065400a98256a13d5975efb39f05c06bb323346
  languageName: node
  linkType: hard

"ejson@npm:2.2.3":
  version: 2.2.3
  resolution: "ejson@npm:2.2.3"
  checksum: 10c0/648ea347f5e57441b7b9341adc6de244445b6da1d0e7747ea7a083f906299b92e4c44fc29e6de0b240d8fa4a73159e85f9367780d7af2ecbc50aae8a4e4961ae
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.149":
  version: 1.5.155
  resolution: "electron-to-chromium@npm:1.5.155"
  checksum: 10c0/aee32a0b03282e488352370f6a910de37788b814031020a0e244943450e844e8a41f741d6e5ec70d553dfa4382ef80088034ddc400b48f45de95de331b9ec178
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 10c0/1573d0ae29ab34661b6c63251ff8f5facd24ccf6a823f19417ae8ba8c88ea450325788c67f16c99edec8de4b52ce93a10fe441ece389fd156e88ee7dab9bfa35
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.4.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.0
  resolution: "entities@npm:6.0.0"
  checksum: 10c0/b82a7bd5de282860f3c36a91e815e41e874fd036c83956a568b82729678492eb088359d6f7e0a4f5c00776427263fcba04959b8340fefa430c39b9bce770427e
  languageName: node
  linkType: hard

"env-editor@npm:^0.4.1":
  version: 0.4.2
  resolution: "env-editor@npm:0.4.2"
  checksum: 10c0/edb33583b0ae5197535905cbcefca424796f6afec799604f7578428ee523245edcd7df48d582fdab67dbcc697ed39070057f512e72f94c91ceefdcb432f5eadb
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: "npm:^1.3.4"
  checksum: 10c0/7679b780043c98b01fc546725484e0cfd3071bf5c906bbe358722972f04abf4fc3f0a77988017665bab367f6ef3fc2d0185f7528f45966b83e7c99c02d5509b9
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 10c0/2530479fe8db57eace5e8646c9c2a9c80fa279614986d16dcc6bcaceb63ae77f05a851ba6c43756d816c61d7f4534baf56e3c705e3e0d884818a46808811c507
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"escodegen@npm:^2.0.0":
  version: 2.1.0
  resolution: "escodegen@npm:2.1.0"
  dependencies:
    esprima: "npm:^4.0.1"
    estraverse: "npm:^5.2.0"
    esutils: "npm:^2.0.2"
    source-map: "npm:~0.6.1"
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: bin/escodegen.js
    esgenerate: bin/esgenerate.js
  checksum: 10c0/e1450a1f75f67d35c061bf0d60888b15f62ab63aef9df1901cffc81cffbbb9e8b3de237c5502cf8613a017c1df3a3003881307c78835a1ab54d8c8d2206e01d3
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1, esprima@npm:~4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0, event-target-shim@npm:^5.0.1":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"eventemitter3@npm:^1.1.0":
  version: 1.2.0
  resolution: "eventemitter3@npm:1.2.0"
  checksum: 10c0/8fb54d4b00d87d0d43fe9bfd4f07bb27702a8f9f23cfa3ac854470b6e23d2868501198a74494c54a8585f8d99e0ef7f63ff926623883c13b4f93e585184867a5
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 10c0/4ba5c00c506e6c786b4d6262cfbce90ddc14c10d4667e5c83ae993c9de88aa856033994dd2b35b83e8dc1170e224e66a319fa80adc4c32adcd2379bbc75da814
  languageName: node
  linkType: hard

"events@npm:1.0.2":
  version: 1.0.2
  resolution: "events@npm:1.0.2"
  checksum: 10c0/0364be40a2cf24e6730e5acf091168b7846ff7e3c4666a7854607909da7fa1d0216e8209767fdc5cf8bff3ccc8bda00502e0f5da10bda185a6120e5e15231e20
  languageName: node
  linkType: hard

"exec-async@npm:^2.2.0":
  version: 2.2.0
  resolution: "exec-async@npm:2.2.0"
  checksum: 10c0/9c70693a3d9f53e19cc8ecf26c3b3fc7125bf40051a71cba70d71161d065a6091d3ab1924c56ac1edd68cb98b9fbef29f83e45dcf67ee6b6c4826e0f898ac039
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: 10c0/71d2ad9b36bc25bb8b104b17e830b40a08989be7f7d100b13269aaae7c3784c3e6e1e88a797e9e87523993a25ba27c8958959a554535370672cfb4d824af8989
  languageName: node
  linkType: hard

"expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "expect@npm:29.7.0"
  dependencies:
    "@jest/expect-utils": "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/2eddeace66e68b8d8ee5f7be57f3014b19770caaf6815c7a08d131821da527fb8c8cb7b3dcd7c883d2d3d8d184206a4268984618032d1e4b16dc8d6596475d41
  languageName: node
  linkType: hard

"expo-apple-authentication@npm:~7.2.4":
  version: 7.2.4
  resolution: "expo-apple-authentication@npm:7.2.4"
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 10c0/466270514595f267731e3ae0077ed36a944348d2f474333f9feafd88133f2bf1e609fbd8e5660771f80152ca9c164e4d12c7783261b7dc4185f3350a2730db06
  languageName: node
  linkType: hard

"expo-asset@npm:~11.1.5":
  version: 11.1.5
  resolution: "expo-asset@npm:11.1.5"
  dependencies:
    "@expo/image-utils": "npm:^0.7.4"
    expo-constants: "npm:~17.1.5"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/0cb762147b1e6becf204c8445c9169fb8465ade493f747cda12551fdbc20908d33406e4a2e41451b27fda40e5691e682820e841810918b4b140a4007136fcbd3
  languageName: node
  linkType: hard

"expo-constants@npm:~17.1.5, expo-constants@npm:~17.1.6":
  version: 17.1.6
  resolution: "expo-constants@npm:17.1.6"
  dependencies:
    "@expo/config": "npm:~11.0.9"
    "@expo/env": "npm:~1.0.5"
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 10c0/922b919521c5b9e92b201c4d83cdae05bd9560955a97c89c2fab73afc5879204c199b2011fd2fa2d2c4e445bc6e69f1c6b6d068bdd8933c44029bc62bd3c0da8
  languageName: node
  linkType: hard

"expo-file-system@npm:~18.1.10":
  version: 18.1.10
  resolution: "expo-file-system@npm:18.1.10"
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 10c0/cd0bfacec0ac78985216a71fb56ff3f2ad62c533a7d3c5a67705e3bf010b71ab30fcb3767429b926284191e6c0e9944e1150abc453788ae6d891e8474d293e5b
  languageName: node
  linkType: hard

"expo-font@npm:~13.3.1":
  version: 13.3.1
  resolution: "expo-font@npm:13.3.1"
  dependencies:
    fontfaceobserver: "npm:^2.1.0"
  peerDependencies:
    expo: "*"
    react: "*"
  checksum: 10c0/aefcf1a1a413f11bcd8b1a4948ff725a864710a517cf15d6fd17c80b5b261595502dd2afa97044cb32d0a65371d6104c69df872c4d8cf4e24e8129e3073f5547
  languageName: node
  linkType: hard

"expo-haptics@npm:^14.0.1":
  version: 14.1.4
  resolution: "expo-haptics@npm:14.1.4"
  peerDependencies:
    expo: "*"
  checksum: 10c0/c8d4dd80fec8949b39af27b718383526b268b56cae4465af57ea7c5d470b34933c03574dc9c0baca06cf81018cc1eb63671a84a273abca685efb02cfd1604e3e
  languageName: node
  linkType: hard

"expo-image-loader@npm:~5.1.0":
  version: 5.1.0
  resolution: "expo-image-loader@npm:5.1.0"
  peerDependencies:
    expo: "*"
  checksum: 10c0/2897a2c3623f752cc277677b170ca9b3db826439641ccc93dcc3a94ece76dcbf084ecc108eddfebb42c34bcb339ef00996726995112c99561f262696c3e23300
  languageName: node
  linkType: hard

"expo-image-picker@npm:^16.0.6":
  version: 16.1.4
  resolution: "expo-image-picker@npm:16.1.4"
  dependencies:
    expo-image-loader: "npm:~5.1.0"
  peerDependencies:
    expo: "*"
  checksum: 10c0/7e27458a48140cea2864564db40ff102525302ca60e9526838ae265523dba8b1d9ceea3a2ac94c1eaece78f4cd1ded0f1a2391517e61a3fd8a6f17b06e337293
  languageName: node
  linkType: hard

"expo-image@npm:~2.1.7":
  version: 2.1.7
  resolution: "expo-image@npm:2.1.7"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
    react-native-web: "*"
  peerDependenciesMeta:
    react-native-web:
      optional: true
  checksum: 10c0/19f1025b031b3ec344cdf7939334063c3a0487bd604bf7c8c7620e9fd6ddde9192749a759e4e2ba59e3a75ffcbfa897ced27faa6af7240cdc855c1681c097945
  languageName: node
  linkType: hard

"expo-keep-awake@npm:~14.1.4":
  version: 14.1.4
  resolution: "expo-keep-awake@npm:14.1.4"
  peerDependencies:
    expo: "*"
    react: "*"
  checksum: 10c0/9d1993f7b17e6c36d707501ba9d983fe7e640317af7f2ecee17255f2c8c7bae39bdb3dfffcc67afcd36457bb60a6799dbaa2414922507ef01298cbe34e8eae8e
  languageName: node
  linkType: hard

"expo-linear-gradient@npm:^14.1.4":
  version: 14.1.4
  resolution: "expo-linear-gradient@npm:14.1.4"
  peerDependencies:
    expo: "*"
    react: "*"
    react-native: "*"
  checksum: 10c0/0ac54e02d5f054d0b8baf32ebfc7af5057f9b6ee2da2c36e3f2bf566b85860a29078a431dc4148bd0f35152008501209fbadb5ced83453ec6a513b949aaf1a6d
  languageName: node
  linkType: hard

"expo-linking@npm:~7.1.5":
  version: 7.1.5
  resolution: "expo-linking@npm:7.1.5"
  dependencies:
    expo-constants: "npm:~17.1.6"
    invariant: "npm:^2.2.4"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/ed976f27aab12063702b5e4fca56c564133f01f8efa7e9980b7aa8de17ad987cf8cb7ee9117015672193e9400b785ec289a7553c734cddc807b17495d4551b98
  languageName: node
  linkType: hard

"expo-local-authentication@npm:~16.0.4":
  version: 16.0.4
  resolution: "expo-local-authentication@npm:16.0.4"
  dependencies:
    invariant: "npm:^2.2.4"
  peerDependencies:
    expo: "*"
  checksum: 10c0/42ddd04bf9b76b67c987ed68df2c467d0bdc007ef879c724453cb2e6729a1ba61612b510ebc13bf10bf0dc84da4c5da86635ff34bd829955becc600264555d28
  languageName: node
  linkType: hard

"expo-location@npm:~18.1.5":
  version: 18.1.5
  resolution: "expo-location@npm:18.1.5"
  peerDependencies:
    expo: "*"
  checksum: 10c0/e054495dd7d30f1b75475dac151f0a7fda60fcd6d8068c41b0cacc0958a62c0270d1f671d6e62a268fc4c5196cbf9195913d9a5d55acb98812e444ca83a46adb
  languageName: node
  linkType: hard

"expo-media-library@npm:~17.1.6":
  version: 17.1.6
  resolution: "expo-media-library@npm:17.1.6"
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 10c0/4080d351e6676f35643b7263082df8380de065b40b03829c118595f9fa3df87cbb2d11bb256b6c61e055b20b721c4088ba7112a3a591be54231e369c2c89ca80
  languageName: node
  linkType: hard

"expo-modules-autolinking@npm:2.1.10":
  version: 2.1.10
  resolution: "expo-modules-autolinking@npm:2.1.10"
  dependencies:
    "@expo/spawn-async": "npm:^1.7.2"
    chalk: "npm:^4.1.0"
    commander: "npm:^7.2.0"
    find-up: "npm:^5.0.0"
    glob: "npm:^10.4.2"
    require-from-string: "npm:^2.0.2"
    resolve-from: "npm:^5.0.0"
  bin:
    expo-modules-autolinking: bin/expo-modules-autolinking.js
  checksum: 10c0/bee6ae8ebf67ac37baf6efaf1fae4f2ba06c2e03cd3cd9146c3393a5550a4cbf25aebe3229f85b7373b6596675c93740f7226fb2c4a6fa32b47c2332ff1d33cd
  languageName: node
  linkType: hard

"expo-modules-core@npm:2.3.13":
  version: 2.3.13
  resolution: "expo-modules-core@npm:2.3.13"
  dependencies:
    invariant: "npm:^2.2.4"
  checksum: 10c0/34cebb2af1d4b3ec58831274e6f8873ff699b36a7d749a1d8bc6b05ee321cc2a5d898f1224c3931fd825739dd554634445867eb5c148a95ec2f4412dc359ccbe
  languageName: node
  linkType: hard

"expo-router@npm:~5.0.7":
  version: 5.0.7
  resolution: "expo-router@npm:5.0.7"
  dependencies:
    "@expo/metro-runtime": "npm:5.0.4"
    "@expo/server": "npm:^0.6.2"
    "@radix-ui/react-slot": "npm:1.2.0"
    "@react-navigation/bottom-tabs": "npm:^7.3.10"
    "@react-navigation/native": "npm:^7.1.6"
    "@react-navigation/native-stack": "npm:^7.3.10"
    client-only: "npm:^0.0.1"
    invariant: "npm:^2.2.4"
    react-fast-compare: "npm:^3.2.2"
    react-native-is-edge-to-edge: "npm:^1.1.6"
    schema-utils: "npm:^4.0.1"
    semver: "npm:~7.6.3"
    server-only: "npm:^0.0.1"
    shallowequal: "npm:^1.1.0"
  peerDependencies:
    "@react-navigation/drawer": ^7.3.9
    expo: "*"
    expo-constants: "*"
    expo-linking: "*"
    react-native-reanimated: "*"
    react-native-safe-area-context: "*"
    react-native-screens: "*"
  peerDependenciesMeta:
    "@react-navigation/drawer":
      optional: true
    "@testing-library/jest-native":
      optional: true
    react-native-reanimated:
      optional: true
  checksum: 10c0/a8fa0435199fd3b6aa7444a01c450003ee459930896f108c2622ca7feaa16ac1a4cd5636ce8e0b7a221213f684a280c55b9805618b28288ded302755296774ad
  languageName: node
  linkType: hard

"expo-secure-store@npm:~14.2.3":
  version: 14.2.3
  resolution: "expo-secure-store@npm:14.2.3"
  peerDependencies:
    expo: "*"
  checksum: 10c0/c1414e2a0a470a4531ae8b6f04e355184e29f61c41c988f9c9f6a3fa2a3f563382a7d44976d8ed560165288993971d295d4071e4921fe17105f3b807180e6f06
  languageName: node
  linkType: hard

"expo-sharing@npm:~13.1.5":
  version: 13.1.5
  resolution: "expo-sharing@npm:13.1.5"
  peerDependencies:
    expo: "*"
  checksum: 10c0/abd35fd0be993a5cf4fcd3010b3e3ab8dca35284c0d4a0a1184721b614673715c65a11d3104b779bb42633b66129c19bd049f5de387ba0c6bcf5efd14653af11
  languageName: node
  linkType: hard

"expo-splash-screen@npm:~0.30.8":
  version: 0.30.8
  resolution: "expo-splash-screen@npm:0.30.8"
  dependencies:
    "@expo/prebuild-config": "npm:^9.0.5"
  peerDependencies:
    expo: "*"
  checksum: 10c0/dde6c8af133e19c50aac00fbbb218877ae63e6ad68ce0e6ff7a46741e6fa8df458b0ce6d094d0d235c818fcb1eb5d7f9a0a6afa666cd1709fe2bdd65e3c548a5
  languageName: node
  linkType: hard

"expo-status-bar@npm:~2.2.3":
  version: 2.2.3
  resolution: "expo-status-bar@npm:2.2.3"
  dependencies:
    react-native-edge-to-edge: "npm:1.6.0"
    react-native-is-edge-to-edge: "npm:^1.1.6"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/8deee621bd94336c9f9bab500f199f6ec5320eaf448c3e4b26e762cf0d0ad34d08ccae9753124c7e966c92cb370eb7440a0a8afba33e904f4a9964e5da1346d0
  languageName: node
  linkType: hard

"expo-system-ui@npm:~5.0.7":
  version: 5.0.7
  resolution: "expo-system-ui@npm:5.0.7"
  dependencies:
    "@react-native/normalize-colors": "npm:0.79.2"
    debug: "npm:^4.3.2"
  peerDependencies:
    expo: "*"
    react-native: "*"
    react-native-web: "*"
  peerDependenciesMeta:
    react-native-web:
      optional: true
  checksum: 10c0/5281f3237f0a509c0960a2991026ff36218309fa53ddebea0bba3a3b0ff81d1a8ca89ccc4f74b12a9929a770b937f682ed6d2141ad812ef942b4fa94b1ba9491
  languageName: node
  linkType: hard

"expo-web-browser@npm:^14.1.6":
  version: 14.1.6
  resolution: "expo-web-browser@npm:14.1.6"
  peerDependencies:
    expo: "*"
    react-native: "*"
  checksum: 10c0/a88fa5b68a4ac428df1379488b3dd4965cba438c4c872d2b7c82378dcfa270008eca95cf8961d484e35ce8baadfe51861f34a6daff839cd81b9f1449f992cc2e
  languageName: node
  linkType: hard

"expo@npm:^53.0.9":
  version: 53.0.9
  resolution: "expo@npm:53.0.9"
  dependencies:
    "@babel/runtime": "npm:^7.20.0"
    "@expo/cli": "npm:0.24.13"
    "@expo/config": "npm:~11.0.10"
    "@expo/config-plugins": "npm:~10.0.2"
    "@expo/fingerprint": "npm:0.12.4"
    "@expo/metro-config": "npm:0.20.14"
    "@expo/vector-icons": "npm:^14.0.0"
    babel-preset-expo: "npm:~13.1.11"
    expo-asset: "npm:~11.1.5"
    expo-constants: "npm:~17.1.6"
    expo-file-system: "npm:~18.1.10"
    expo-font: "npm:~13.3.1"
    expo-keep-awake: "npm:~14.1.4"
    expo-modules-autolinking: "npm:2.1.10"
    expo-modules-core: "npm:2.3.13"
    react-native-edge-to-edge: "npm:1.6.0"
    whatwg-url-without-unicode: "npm:8.0.0-3"
  peerDependencies:
    "@expo/dom-webview": "*"
    "@expo/metro-runtime": "*"
    react: "*"
    react-native: "*"
    react-native-webview: "*"
  peerDependenciesMeta:
    "@expo/dom-webview":
      optional: true
    "@expo/metro-runtime":
      optional: true
    react-native-webview:
      optional: true
  bin:
    expo: bin/cli
    expo-modules-autolinking: bin/autolinking
    fingerprint: bin/fingerprint
  checksum: 10c0/7c8392fd3805b2b208da67de2757d23688674799efb58e918a61d4c294a60a79951b82d4d772bab961bad9b1402278a486408fa11c105e0552373554ba434ed1
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"fast-base64-decode@npm:^1.0.0":
  version: 1.0.0
  resolution: "fast-base64-decode@npm:1.0.0"
  checksum: 10c0/6d8feab513222a463d1cb58d24e04d2e04b0791ac6559861f99543daaa590e2636d040d611b40a50799bfb5c5304265d05e3658b5adf6b841a50ef6bf833d821
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10c0/74a513c2af0584448aee71ce56005185f81239eab7a2343110e5bad50c39ad4fb19c5a6f99783ead1cac7ccaf3461a6034fda89fffa2b30b6d99b9f21c2f9d29
  languageName: node
  linkType: hard

"fast-xml-parser@npm:4.4.1":
  version: 4.4.1
  resolution: "fast-xml-parser@npm:4.4.1"
  dependencies:
    strnum: "npm:^1.0.5"
  bin:
    fxparser: src/cli/cli.js
  checksum: 10c0/7f334841fe41bfb0bf5d920904ccad09cefc4b5e61eaf4c225bf1e1bb69ee77ef2147d8942f783ee8249e154d1ca8a858e10bda78a5d78b8bed3f48dcee9bf33
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: "npm:2.1.1"
  checksum: 10c0/feae89ac148adb8f6ae8ccd87632e62b13563e6fb114cacb5265c51f585b17e2e268084519fb2edd133872f1d47a18e6bfd7e5e08625c0d41b93149694187581
  languageName: node
  linkType: hard

"fbjs-css-vars@npm:^1.0.0":
  version: 1.0.2
  resolution: "fbjs-css-vars@npm:1.0.2"
  checksum: 10c0/dfb64116b125a64abecca9e31477b5edb9a2332c5ffe74326fe36e0a72eef7fc8a49b86adf36c2c293078d79f4524f35e80f5e62546395f53fb7c9e69821f54f
  languageName: node
  linkType: hard

"fbjs@npm:^3.0.4":
  version: 3.0.5
  resolution: "fbjs@npm:3.0.5"
  dependencies:
    cross-fetch: "npm:^3.1.5"
    fbjs-css-vars: "npm:^1.0.0"
    loose-envify: "npm:^1.0.0"
    object-assign: "npm:^4.1.0"
    promise: "npm:^7.1.1"
    setimmediate: "npm:^1.0.5"
    ua-parser-js: "npm:^1.0.35"
  checksum: 10c0/66d0a2fc9a774f9066e35ac2ac4bf1245931d27f3ac287c7d47e6aa1fc152b243c2109743eb8f65341e025621fb51a12038fadb9fd8fda2e3ddae04ebab06f91
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.4
  resolution: "fdir@npm:6.4.4"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/6ccc33be16945ee7bc841e1b4178c0b4cf18d3804894cb482aa514651c962a162f96da7ffc6ebfaf0df311689fb70091b04dd6caffe28d56b9ebdc0e7ccadfdd
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: 10c0/071e0886b2b50238ca5026c5bbf58c26a7c1a1f720773b8c7813d16ba93d0200de977af14ac143c5ac18f666b2cfc83073f3a5fe6a4e996c49e0863d5500fccf
  languageName: node
  linkType: hard

"finalhandler@npm:1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:~2.3.0"
    parseurl: "npm:~1.3.3"
    statuses: "npm:~1.5.0"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/6a96e1f5caab085628c11d9fdceb82ba608d5e426c6913d4d918409baa271037a47f28fbba73279e8ad614f0b8fa71ea791d265e408d760793829edd8c2f4584
  languageName: node
  linkType: hard

"find-babel-config@npm:^2.1.1":
  version: 2.1.2
  resolution: "find-babel-config@npm:2.1.2"
  dependencies:
    json5: "npm:^2.2.3"
  checksum: 10c0/c9151b23d636378eae11aa761b0af41d5f67d5479e3ebfca7b0ec7feef91723f14242d243342783b89e6c51fc5b4120086eacf5d8a1a335cf7bae4b0ac89f493
  languageName: node
  linkType: hard

"find-cache-dir@npm:^2.0.0":
  version: 2.1.0
  resolution: "find-cache-dir@npm:2.1.0"
  dependencies:
    commondir: "npm:^1.0.1"
    make-dir: "npm:^2.0.0"
    pkg-dir: "npm:^3.0.0"
  checksum: 10c0/556117fd0af14eb88fb69250f4bba9e905e7c355c6136dff0e161b9cbd1f5285f761b778565a278da73a130f42eccc723d7ad4c002ae547ed1d698d39779dabb
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: "npm:^3.0.0"
  checksum: 10c0/2c2e7d0a26db858e2f624f39038c74739e38306dee42b45f404f770db357947be9d0d587f1cac72d20c114deb38aa57316e879eb0a78b17b46da7dab0a3bd6e3
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"find-yarn-workspace-root@npm:^2.0.0":
  version: 2.0.0
  resolution: "find-yarn-workspace-root@npm:2.0.0"
  dependencies:
    micromatch: "npm:^4.0.2"
  checksum: 10c0/b0d3843013fbdaf4e57140e0165889d09fa61745c9e85da2af86e54974f4cc9f1967e40f0d8fc36a79d53091f0829c651d06607d552582e53976f3cd8f4e5689
  languageName: node
  linkType: hard

"flow-enums-runtime@npm:^0.0.6":
  version: 0.0.6
  resolution: "flow-enums-runtime@npm:0.0.6"
  checksum: 10c0/f0b9ca52dbf9cf30264ebf1af034ac7b80fb5e5ef009efc789b89a90aa17349a3ff5672b3b27c6eb89d5e02808fc0dfb7effbfc5a793451694d6cce48774d51e
  languageName: node
  linkType: hard

"flow-parser@npm:0.*":
  version: 0.271.0
  resolution: "flow-parser@npm:0.271.0"
  checksum: 10c0/e5a1f8062c873fae9dd7f390a3e439b9fe59ccf4602fc582ad5795a58f13e0e7d2c3f0f521ed91d3c051c2368eb6daf1581bacca000189e3c2765e0f95cf3c65
  languageName: node
  linkType: hard

"fontfaceobserver@npm:^2.1.0":
  version: 2.3.0
  resolution: "fontfaceobserver@npm:2.3.0"
  checksum: 10c0/9b539d5021757d3ed73c355bdb839296d6654de473a992aa98993ef46d951f0361545323de68f6d70c5334d7e3e9f409c1ae7a03c168b00cb0f6c5dea6c77bfa
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.2
  resolution: "form-data@npm:4.0.2"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/e534b0cf025c831a0929bf4b9bbe1a9a6b03e273a8161f9947286b9b13bf8fb279c6944aae0070c4c311100c6d6dbb815cd955dc217728caf73fad8dc5b8ee9c
  languageName: node
  linkType: hard

"freeport-async@npm:^2.0.0":
  version: 2.0.0
  resolution: "freeport-async@npm:2.0.0"
  checksum: 10c0/421828d1a689695b6c8122d310fd8941af99ebe0b5793e3f8d49aa5923ce580b6c4dd6b7470d46983e60839c302f6c793a8541dbab80817396cdde2b04c83c90
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: "npm:^1.0.0"
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/9b808bd884beff5cb940773018179a6b94a966381d005479f00adda6b44e5e3d4abf765135773d849cc27efe68c349e4a7b86acd7d3306d5932c14f3a4b17a92
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2, fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A^2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: 10c0/e34cdf447fdf1902a1f6d5af737eaadf606d2ee3518287abde8910e04159368c268568174b2e71102b87b26c2020486f126bfca9c4fb1ceb986ff99b52ecd1be
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"getenv@npm:^1.0.0":
  version: 1.0.0
  resolution: "getenv@npm:1.0.0"
  checksum: 10c0/9661c5996c7622e12eab1d23448474ae51dbec6f8862eed903ebaa864dcd332895441c23d962e3ff5c180a9e3dff6cb1f569a115e1447db4acb52af2d880d655
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.4.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.1.1, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"glob@npm:^9.3.3":
  version: 9.3.5
  resolution: "glob@npm:9.3.5"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    minimatch: "npm:^8.0.2"
    minipass: "npm:^4.2.4"
    path-scurry: "npm:^1.6.1"
  checksum: 10c0/2f6c2b9ee019ee21dc258ae97a88719614591e4c979cb4580b1b9df6f0f778a3cb38b4bdaf18dfa584637ea10f89a3c5f2533a5e449cf8741514ad18b0951f2e
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.11, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10c0/1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-own-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-own-prop@npm:2.0.0"
  checksum: 10c0/2745497283d80228b5c5fbb8c63ab1029e604bce7db8d4b36255e427b3695b2153dc978b176674d0dd2a23f132809e04d7ef41fefc0ab85870a5caa918c5c0d9
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hermes-estree@npm:0.25.1":
  version: 0.25.1
  resolution: "hermes-estree@npm:0.25.1"
  checksum: 10c0/48be3b2fa37a0cbc77a112a89096fa212f25d06de92781b163d67853d210a8a5c3784fac23d7d48335058f7ed283115c87b4332c2a2abaaccc76d0ead1a282ac
  languageName: node
  linkType: hard

"hermes-estree@npm:0.28.1":
  version: 0.28.1
  resolution: "hermes-estree@npm:0.28.1"
  checksum: 10c0/aa00f437c82099b9043e384b529c75de21d0111b792ab7480fe992975b5f9535a8581664789db197824a7825ea66d2fd70eb20cb568c5315804421deaf009500
  languageName: node
  linkType: hard

"hermes-parser@npm:0.25.1":
  version: 0.25.1
  resolution: "hermes-parser@npm:0.25.1"
  dependencies:
    hermes-estree: "npm:0.25.1"
  checksum: 10c0/3abaa4c6f1bcc25273f267297a89a4904963ea29af19b8e4f6eabe04f1c2c7e9abd7bfc4730ddb1d58f2ea04b6fee74053d8bddb5656ec6ebf6c79cc8d14202c
  languageName: node
  linkType: hard

"hermes-parser@npm:0.28.1":
  version: 0.28.1
  resolution: "hermes-parser@npm:0.28.1"
  dependencies:
    hermes-estree: "npm:0.28.1"
  checksum: 10c0/c6d3c01fb1ea5232f4587b6b038f5c2c6414932e7c48efbe156ab160e2bcaac818c9eb2f828f30967a24b40f543cad503baed0eedf5a7e877852ed271915981f
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.0":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: "npm:^16.7.0"
  checksum: 10c0/fe0889169e845d738b59b64badf5e55fa3cf20454f9203d1eb088df322d49d4318df774828e789898dcb280e8a5521bb59b3203385662ca5e9218a6ca5820e74
  languageName: node
  linkType: hard

"hosted-git-info@npm:^7.0.0":
  version: 7.0.2
  resolution: "hosted-git-info@npm:7.0.2"
  dependencies:
    lru-cache: "npm:^10.0.1"
  checksum: 10c0/b19dbd92d3c0b4b0f1513cf79b0fc189f54d6af2129eeb201de2e9baaa711f1936929c848b866d9c8667a0f956f34bf4f07418c12be1ee9ca74fd9246335ca1f
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^3.0.0":
  version: 3.0.0
  resolution: "html-encoding-sniffer@npm:3.0.0"
  dependencies:
    whatwg-encoding: "npm:^2.0.0"
  checksum: 10c0/b17b3b0fb5d061d8eb15121c3b0b536376c3e295ecaf09ba48dd69c6b6c957839db124fe1e2b3f11329753a4ee01aa7dedf63b7677999e86da17fbbdd82c5386
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10c0/208e8a12de1a6569edbb14544f4567e6ce8ecc30b9394fcaa4e7bb1e60c12a7c9a1ed27e31290817157e8626f3a4f29e76c8747030822eb84a6abb15c255f0a0
  languageName: node
  linkType: hard

"html2canvas@npm:^1.4.1":
  version: 1.4.1
  resolution: "html2canvas@npm:1.4.1"
  dependencies:
    css-line-break: "npm:^2.1.0"
    text-segmentation: "npm:^1.0.3"
  checksum: 10c0/6de86f75762b00948edf2ea559f16da0a1ec3facc4a8a7d3f35fcec59bb0c5970463478988ae3d9082152e0173690d46ebf4082e7ac803dd4817bae1d355c0db
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": "npm:2"
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/32a05e413430b2c1e542e5c74b38a9f14865301dd69dff2e53ddb684989440e3d2ce0c4b64d25eb63cf6283e6265ff979a61cf93e3ca3d23047ddfdc8df34a32
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.1":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1, https-proxy-agent@npm:^7.0.5":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"hyphenate-style-name@npm:^1.0.3":
  version: 1.1.0
  resolution: "hyphenate-style-name@npm:1.1.0"
  checksum: 10c0/bfe88deac2414a41a0d08811e277c8c098f23993d6a1eb17f14a0f11b54c4d42865a63d3cfe1914668eefb9a188e2de58f38b55a179a238fd1fef606893e194f
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"image-size@npm:^1.0.2":
  version: 1.2.1
  resolution: "image-size@npm:1.2.1"
  dependencies:
    queue: "npm:6.0.2"
  bin:
    image-size: bin/image-size.js
  checksum: 10c0/f8b3c19d4476513f1d7e55c3e6db80997b315444743e2040d545cbcaee59be03d2eb40c46be949a8372697b7003fdb0c04925d704390a7f606bc8181e25c0ed4
  languageName: node
  linkType: hard

"import-fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-fresh@npm:2.0.0"
  dependencies:
    caller-path: "npm:^2.0.0"
    resolve-from: "npm:^3.0.0"
  checksum: 10c0/116c55ee5215a7839062285b60df85dbedde084c02111dc58c1b9d03ff7876627059f4beb16cdc090a3db21fea9022003402aa782139dc8d6302589038030504
  languageName: node
  linkType: hard

"import-fresh@npm:^3.3.0":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.2.0
  resolution: "import-local@npm:3.2.0"
  dependencies:
    pkg-dir: "npm:^4.2.0"
    resolve-cwd: "npm:^3.0.0"
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 10c0/94cd6367a672b7e0cb026970c85b76902d2710a64896fa6de93bd5c571dd03b228c5759308959de205083e3b1c61e799f019c9e36ee8e9c523b993e1057f0433
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10c0/ec93838d2328b619532e4f1ff05df7909760b6f66d9c9e2ded11e5c1897d6f2f9980c54dd638f88654b00919ce31e827040631eab0a3969e4d1abefa0719516a
  languageName: node
  linkType: hard

"inline-style-prefixer@npm:^7.0.1":
  version: 7.0.1
  resolution: "inline-style-prefixer@npm:7.0.1"
  dependencies:
    css-in-js-utils: "npm:^3.1.0"
  checksum: 10c0/15da5a396b7f286b5b6742efe315218cd577bc96b43de08aeb76af7697d9f1ab3bfc66cf19fad2173957dd5d617a790240b9d51898bdcf4c2efb40d3f8bcb370
  languageName: node
  linkType: hard

"intl-messageformat@npm:^10.1.0":
  version: 10.7.16
  resolution: "intl-messageformat@npm:10.7.16"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:2.3.4"
    "@formatjs/fast-memoize": "npm:2.2.7"
    "@formatjs/icu-messageformat-parser": "npm:2.11.2"
    tslib: "npm:^2.8.0"
  checksum: 10c0/537735bf6439f0560f132895d117df6839957ac04cdd58d861f6da86803d40bfc19059e3d341ddb8de87214b73a6329b57f9acdb512bb0f745dcf08729507b9b
  languageName: node
  linkType: hard

"invariant@npm:^2.1.1, invariant@npm:^2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: "npm:^1.0.0"
  checksum: 10c0/5af133a917c0bcf65e84e7f23e779e7abc1cd49cb7fdc62d00d1de74b0d8c1b5ee74ac7766099fb3be1b05b26dfc67bab76a17030d2fe7ea2eef867434362dfc
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-directory@npm:^0.3.1":
  version: 0.3.1
  resolution: "is-directory@npm:0.3.1"
  checksum: 10c0/1c39c7d1753b04e9483b89fb88908b8137ab4743b6f481947e97ccf93ecb384a814c8d3f0b95b082b149c5aa19c3e9e4464e2791d95174bce95998c26bb1974b
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10c0/e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: 10c0/2957cab387997a466cd0bf5c1b6047bd21ecb32bdcfd8996b15747aa01002c1c88731802f1b3d34ac99f4f6874b626418bd118658cf39380fe5fff32a3af9c4d
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: 10c0/e5c9814cdaa627a9ad0a0964ded0e0491bfd9ace405c49a5d63c88b30a162f1512c069d5b80997893c4d0181eadc3fed02b4ab4b81059aba5620bfcdfdeb9c53
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10c0/f050fdd5203d9c81e8c4df1b3ff461c4bc64e8b5ca383bcdde46131361d0a678e80bcf00b5257646f6c636197629644d53bd8e2375aea633de09a82d57e942f4
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: 10c0/b73e2f22bc863b0939941d369486d308b43d7aef1f9439705e3582bfccaa4516406865e32c968a35f97a99396dac84e2624e67b0a16b0a15086a785e16ce7db9
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1, is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10c0/a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10c0/4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10c0/03344f5064a82f099a0cd1a8a407f4c0d20b7b8485e8e816c39f249e9416b06c322e8dec5b842b6bb8a06de0af9cb48e7bc1b5352f0fadc2f0abac033db3d4db
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10c0/6c7ff2106769e5f592ded1fb418f9f73b4411fd5a084387a5410538332b6567cd1763ff6b6cadca9b9eb2c443cce2f7ea7d7f1b8d315f9ce58539793b1e0922b
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": "npm:^7.12.3"
    "@babel/parser": "npm:^7.14.7"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^6.3.0"
  checksum: 10c0/8a1bdf3e377dcc0d33ec32fe2b6ecacdb1e4358fd0eb923d4326bb11c67622c0ceb99600a680f3dad5d29c66fc1991306081e339b4d43d0b8a2ab2e1d910a6ee
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^6.0.0":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": "npm:^7.23.9"
    "@babel/parser": "npm:^7.23.9"
    "@istanbuljs/schema": "npm:^0.1.3"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^7.5.4"
  checksum: 10c0/a1894e060dd2a3b9f046ffdc87b44c00a35516f5e6b7baf4910369acca79e506fc5323a816f811ae23d82334b38e3ddeb8b3b331bd2c860540793b59a8689128
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/84323afb14392de8b6a5714bd7e9af845cfbd56cfe71ed276cda2f5f1201aea673c7111901227ee33e68e4364e288d73861eb2ed48f6679d1e69a43b6d9b3ba7
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: "npm:^4.1.1"
    istanbul-lib-coverage: "npm:^3.0.0"
    source-map: "npm:^0.6.1"
  checksum: 10c0/19e4cc405016f2c906dff271a76715b3e881fa9faeb3f09a86cb99b8512b3a5ed19cadfe0b54c17ca0e54c1142c9c6de9330d65506e35873994e06634eebeb66
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10c0/a379fadf9cf8dc5dfe25568115721d4a7eb82fbd50b005a6672aff9c6989b20cc9312d7865814e0859cd8df58cbf664482e1d3604be0afde1f7fc3ccc1394a51
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jalali-plugin-dayjs@npm:^1.1.4":
  version: 1.1.4
  resolution: "jalali-plugin-dayjs@npm:1.1.4"
  checksum: 10c0/2a8fada01435fab90d754d87e383385a9b80f303cee3bf8425db91c7f5bd7be48f0995774b21d903ae840499f12762dc34392d757e3feb4516af6b7e0a2b651b
  languageName: node
  linkType: hard

"javascript-natural-sort@npm:^0.7.1":
  version: 0.7.1
  resolution: "javascript-natural-sort@npm:0.7.1"
  checksum: 10c0/340f8ffc5d30fb516e06dc540e8fa9e0b93c865cf49d791fed3eac3bdc5fc71f0066fc81d44ec1433edc87caecaf9f13eec4a1fce8c5beafc709a71eaedae6fe
  languageName: node
  linkType: hard

"jest-changed-files@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-changed-files@npm:29.7.0"
  dependencies:
    execa: "npm:^5.0.0"
    jest-util: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
  checksum: 10c0/e071384d9e2f6bb462231ac53f29bff86f0e12394c1b49ccafbad225ce2ab7da226279a8a94f421949920bef9be7ef574fd86aee22e8adfa149be73554ab828b
  languageName: node
  linkType: hard

"jest-circus@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-circus@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/expect": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    co: "npm:^4.6.0"
    dedent: "npm:^1.0.0"
    is-generator-fn: "npm:^2.0.0"
    jest-each: "npm:^29.7.0"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
    pure-rand: "npm:^6.0.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10c0/8d15344cf7a9f14e926f0deed64ed190c7a4fa1ed1acfcd81e4cc094d3cc5bf7902ebb7b874edc98ada4185688f90c91e1747e0dfd7ac12463b097968ae74b5e
  languageName: node
  linkType: hard

"jest-cli@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-cli@npm:29.7.0"
  dependencies:
    "@jest/core": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    create-jest: "npm:^29.7.0"
    exit: "npm:^0.1.2"
    import-local: "npm:^3.0.2"
    jest-config: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    yargs: "npm:^17.3.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 10c0/a658fd55050d4075d65c1066364595962ead7661711495cfa1dfeecf3d6d0a8ffec532f3dbd8afbb3e172dd5fd2fb2e813c5e10256e7cf2fea766314942fb43a
  languageName: node
  linkType: hard

"jest-config@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-config@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/test-sequencer": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    babel-jest: "npm:^29.7.0"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    deepmerge: "npm:^4.2.2"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    jest-circus: "npm:^29.7.0"
    jest-environment-node: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-runner: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    parse-json: "npm:^5.2.0"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-json-comments: "npm:^3.1.1"
  peerDependencies:
    "@types/node": "*"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    ts-node:
      optional: true
  checksum: 10c0/bab23c2eda1fff06e0d104b00d6adfb1d1aabb7128441899c9bff2247bd26710b050a5364281ce8d52b46b499153bf7e3ee88b19831a8f3451f1477a0246a0f1
  languageName: node
  linkType: hard

"jest-diff@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-diff@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    diff-sequences: "npm:^29.6.3"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/89a4a7f182590f56f526443dde69acefb1f2f0c9e59253c61d319569856c4931eae66b8a3790c443f529267a0ddba5ba80431c585deed81827032b2b2a1fc999
  languageName: node
  linkType: hard

"jest-docblock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-docblock@npm:29.7.0"
  dependencies:
    detect-newline: "npm:^3.0.0"
  checksum: 10c0/d932a8272345cf6b6142bb70a2bb63e0856cc0093f082821577ea5bdf4643916a98744dfc992189d2b1417c38a11fa42466f6111526bc1fb81366f56410f3be9
  languageName: node
  linkType: hard

"jest-each@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-each@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/f7f9a90ebee80cc688e825feceb2613627826ac41ea76a366fa58e669c3b2403d364c7c0a74d862d469b103c843154f8456d3b1c02b487509a12afa8b59edbb4
  languageName: node
  linkType: hard

"jest-environment-jsdom@npm:^29.2.1":
  version: 29.7.0
  resolution: "jest-environment-jsdom@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/jsdom": "npm:^20.0.0"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jsdom: "npm:^20.0.0"
  peerDependencies:
    canvas: ^2.5.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10c0/139b94e2c8ec1bb5a46ce17df5211da65ce867354b3fd4e00fa6a0d1da95902df4cf7881273fc6ea937e5c325d39d6773f0d41b6c469363334de9d489d2c321f
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-environment-node@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/61f04fec077f8b1b5c1a633e3612fc0c9aa79a0ab7b05600683428f1e01a4d35346c474bde6f439f9fcc1a4aa9a2861ff852d079a43ab64b02105d1004b2592b
  languageName: node
  linkType: hard

"jest-expo@npm:~53.0.5":
  version: 53.0.5
  resolution: "jest-expo@npm:53.0.5"
  dependencies:
    "@expo/config": "npm:~11.0.9"
    "@expo/json-file": "npm:^9.1.4"
    "@jest/create-cache-key-function": "npm:^29.2.1"
    "@jest/globals": "npm:^29.2.1"
    babel-jest: "npm:^29.2.1"
    find-up: "npm:^5.0.0"
    jest-environment-jsdom: "npm:^29.2.1"
    jest-snapshot: "npm:^29.2.1"
    jest-watch-select-projects: "npm:^2.0.0"
    jest-watch-typeahead: "npm:2.2.1"
    json5: "npm:^2.2.3"
    lodash: "npm:^4.17.19"
    react-server-dom-webpack: "npm:~19.0.0"
    react-test-renderer: "npm:19.0.0"
    server-only: "npm:^0.0.1"
    stacktrace-js: "npm:^2.0.2"
  peerDependencies:
    expo: "*"
    react-native: "*"
  bin:
    jest: bin/jest.js
  checksum: 10c0/6f58875c812a548fcfc20d15f54bd6fabaf663cd30461d6a289557abbef5dd181aba5054c3c10001c8e00d043b86580ac42a03a3f3a4a2a17fb9926d3fbc8b34
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-get-type@npm:29.6.3"
  checksum: 10c0/552e7a97a983d3c2d4e412a44eb7de0430ff773dd99f7500962c268d6dfbfa431d7d08f919c9d960530e5f7f78eb47f267ad9b318265e5092b3ff9ede0db7c2b
  languageName: node
  linkType: hard

"jest-haste-map@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-haste-map@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/graceful-fs": "npm:^4.1.3"
    "@types/node": "npm:*"
    anymatch: "npm:^3.0.3"
    fb-watchman: "npm:^2.0.0"
    fsevents: "npm:^2.3.2"
    graceful-fs: "npm:^4.2.9"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    walker: "npm:^1.0.8"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/2683a8f29793c75a4728787662972fedd9267704c8f7ef9d84f2beed9a977f1cf5e998c07b6f36ba5603f53cb010c911fe8cd0ac9886e073fe28ca66beefd30c
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-leak-detector@npm:29.7.0"
  dependencies:
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/71bb9f77fc489acb842a5c7be030f2b9acb18574dc9fb98b3100fc57d422b1abc55f08040884bd6e6dbf455047a62f7eaff12aa4058f7cbdc11558718ca6a395
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-matcher-utils@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/0d0e70b28fa5c7d4dce701dc1f46ae0922102aadc24ed45d594dd9b7ae0a8a6ef8b216718d1ab79e451291217e05d4d49a82666e1a3cc2b428b75cd9c933244e
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-message-util@npm:29.7.0"
  dependencies:
    "@babel/code-frame": "npm:^7.12.13"
    "@jest/types": "npm:^29.6.3"
    "@types/stack-utils": "npm:^2.0.0"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10c0/850ae35477f59f3e6f27efac5215f706296e2104af39232bb14e5403e067992afb5c015e87a9243ec4d9df38525ef1ca663af9f2f4766aa116f127247008bd22
  languageName: node
  linkType: hard

"jest-mock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-mock@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/7b9f8349ee87695a309fe15c46a74ab04c853369e5c40952d68061d9dc3159a0f0ed73e215f81b07ee97a9faaf10aebe5877a9d6255068a0977eae6a9ff1d5ac
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: 10c0/86eec0c78449a2de733a6d3e316d49461af6a858070e113c97f75fb742a48c2396ea94150cbca44159ffd4a959f743a47a8b37a792ef6fdad2cf0a5cba973fac
  languageName: node
  linkType: hard

"jest-regex-util@npm:^29.0.0, jest-regex-util@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-regex-util@npm:29.6.3"
  checksum: 10c0/4e33fb16c4f42111159cafe26397118dcfc4cf08bc178a67149fb05f45546a91928b820894572679d62559839d0992e21080a1527faad65daaae8743a5705a3b
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve-dependencies@npm:29.7.0"
  dependencies:
    jest-regex-util: "npm:^29.6.3"
    jest-snapshot: "npm:^29.7.0"
  checksum: 10c0/b6e9ad8ae5b6049474118ea6441dfddd385b6d1fc471db0136f7c8fbcfe97137a9665e4f837a9f49f15a29a1deb95a14439b7aec812f3f99d08f228464930f0d
  languageName: node
  linkType: hard

"jest-resolve@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-pnp-resolver: "npm:^1.2.2"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    resolve: "npm:^1.20.0"
    resolve.exports: "npm:^2.0.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/59da5c9c5b50563e959a45e09e2eace783d7f9ac0b5dcc6375dea4c0db938d2ebda97124c8161310082760e8ebbeff9f6b177c15ca2f57fb424f637a5d2adb47
  languageName: node
  linkType: hard

"jest-runner@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runner@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/environment": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    emittery: "npm:^0.13.1"
    graceful-fs: "npm:^4.2.9"
    jest-docblock: "npm:^29.7.0"
    jest-environment-node: "npm:^29.7.0"
    jest-haste-map: "npm:^29.7.0"
    jest-leak-detector: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-resolve: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-watcher: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
    source-map-support: "npm:0.5.13"
  checksum: 10c0/2194b4531068d939f14c8d3274fe5938b77fa73126aedf9c09ec9dec57d13f22c72a3b5af01ac04f5c1cf2e28d0ac0b4a54212a61b05f10b5d6b47f2a1097bb4
  languageName: node
  linkType: hard

"jest-runtime@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runtime@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/globals": "npm:^29.7.0"
    "@jest/source-map": "npm:^29.6.3"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    cjs-module-lexer: "npm:^1.0.0"
    collect-v8-coverage: "npm:^1.0.0"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-bom: "npm:^4.0.0"
  checksum: 10c0/7cd89a1deda0bda7d0941835434e44f9d6b7bd50b5c5d9b0fc9a6c990b2d4d2cab59685ab3cb2850ed4cc37059f6de903af5a50565d7f7f1192a77d3fd6dd2a6
  languageName: node
  linkType: hard

"jest-snapshot@npm:^29.2.1, jest-snapshot@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-snapshot@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@babel/generator": "npm:^7.7.2"
    "@babel/plugin-syntax-jsx": "npm:^7.7.2"
    "@babel/plugin-syntax-typescript": "npm:^7.7.2"
    "@babel/types": "npm:^7.3.3"
    "@jest/expect-utils": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
    chalk: "npm:^4.0.0"
    expect: "npm:^29.7.0"
    graceful-fs: "npm:^4.2.9"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    natural-compare: "npm:^1.4.0"
    pretty-format: "npm:^29.7.0"
    semver: "npm:^7.5.3"
  checksum: 10c0/6e9003c94ec58172b4a62864a91c0146513207bedf4e0a06e1e2ac70a4484088a2683e3a0538d8ea913bcfd53dc54a9b98a98cdfa562e7fe1d1339aeae1da570
  languageName: node
  linkType: hard

"jest-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-util@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    graceful-fs: "npm:^4.2.9"
    picomatch: "npm:^2.2.3"
  checksum: 10c0/bc55a8f49fdbb8f51baf31d2a4f312fb66c9db1483b82f602c9c990e659cdd7ec529c8e916d5a89452ecbcfae4949b21b40a7a59d4ffc0cd813a973ab08c8150
  languageName: node
  linkType: hard

"jest-validate@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-validate@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    camelcase: "npm:^6.2.0"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    leven: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/a20b930480c1ed68778c739f4739dce39423131bc070cd2505ddede762a5570a256212e9c2401b7ae9ba4d7b7c0803f03c5b8f1561c62348213aba18d9dbece2
  languageName: node
  linkType: hard

"jest-watch-select-projects@npm:^2.0.0":
  version: 2.0.0
  resolution: "jest-watch-select-projects@npm:2.0.0"
  dependencies:
    ansi-escapes: "npm:^4.3.0"
    chalk: "npm:^3.0.0"
    prompts: "npm:^2.2.1"
  checksum: 10c0/c8d9817aff45dd85d307a48ba63dec33a1a7496b1896cb54f446799626e1700afb7755424e360c7a14b15e88b9cf45669cc18339347a407e5eeed829994a7106
  languageName: node
  linkType: hard

"jest-watch-typeahead@npm:2.2.1":
  version: 2.2.1
  resolution: "jest-watch-typeahead@npm:2.2.1"
  dependencies:
    ansi-escapes: "npm:^6.0.0"
    chalk: "npm:^4.0.0"
    jest-regex-util: "npm:^29.0.0"
    jest-watcher: "npm:^29.0.0"
    slash: "npm:^5.0.0"
    string-length: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  peerDependencies:
    jest: ^27.0.0 || ^28.0.0 || ^29.0.0
  checksum: 10c0/2f47433ac6dd1dfd3015182b325108bc95e15dfbb577e7730468172b15b7d91be443f4d68a3849963e1f29e96d031eaf2b79cae6f45e64630383129a2d5e2e2d
  languageName: node
  linkType: hard

"jest-watcher@npm:^29.0.0, jest-watcher@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-watcher@npm:29.7.0"
  dependencies:
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.0.0"
    emittery: "npm:^0.13.1"
    jest-util: "npm:^29.7.0"
    string-length: "npm:^4.0.1"
  checksum: 10c0/ec6c75030562fc8f8c727cb8f3b94e75d831fc718785abfc196e1f2a2ebc9a2e38744a15147170039628a853d77a3b695561ce850375ede3a4ee6037a2574567
  languageName: node
  linkType: hard

"jest-worker@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-worker@npm:29.7.0"
  dependencies:
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10c0/5570a3a005b16f46c131968b8a5b56d291f9bbb85ff4217e31c80bd8a02e7de799e59a54b95ca28d5c302f248b54cbffde2d177c2f0f52ffcee7504c6eabf660
  languageName: node
  linkType: hard

"jest@npm:^29.2.1":
  version: 29.7.0
  resolution: "jest@npm:29.7.0"
  dependencies:
    "@jest/core": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    import-local: "npm:^3.0.2"
    jest-cli: "npm:^29.7.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 10c0/f40eb8171cf147c617cc6ada49d062fbb03b4da666cb8d39cdbfb739a7d75eea4c3ca150fb072d0d273dce0c753db4d0467d54906ad0293f59c54f9db4a09d8b
  languageName: node
  linkType: hard

"jimp-compact@npm:0.16.1":
  version: 0.16.1
  resolution: "jimp-compact@npm:0.16.1"
  checksum: 10c0/2d73bb927d840ce6dc093d089d770eddbb81472635ced7cad1d7c4545d8734aecf5bd3dedf7178a6cfab4d06c9d6cbbf59e5cb274ed99ca11cd4835a6374f16c
  languageName: node
  linkType: hard

"jiti@npm:^1.21.6":
  version: 1.21.7
  resolution: "jiti@npm:1.21.7"
  bin:
    jiti: bin/jiti.js
  checksum: 10c0/77b61989c758ff32407cdae8ddc77f85e18e1a13fc4977110dbd2e05fc761842f5f71bce684d9a01316e1c4263971315a111385759951080bbfe17cbb5de8f7a
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsc-safe-url@npm:^0.2.2, jsc-safe-url@npm:^0.2.4":
  version: 0.2.4
  resolution: "jsc-safe-url@npm:0.2.4"
  checksum: 10c0/429bd645f8a35938f08f5b01c282e5ef55ed8be30a9ca23517b7ca01dcbf84b4b0632042caceab50f8f5c0c1e76816fe3c74de3e59be84da7f89ae1503bd3c68
  languageName: node
  linkType: hard

"jscodeshift@npm:0.15.2":
  version: 0.15.2
  resolution: "jscodeshift@npm:0.15.2"
  dependencies:
    "@babel/core": "npm:^7.23.0"
    "@babel/parser": "npm:^7.23.0"
    "@babel/plugin-transform-class-properties": "npm:^7.22.5"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.23.0"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.22.11"
    "@babel/plugin-transform-optional-chaining": "npm:^7.23.0"
    "@babel/plugin-transform-private-methods": "npm:^7.22.5"
    "@babel/preset-flow": "npm:^7.22.15"
    "@babel/preset-typescript": "npm:^7.23.0"
    "@babel/register": "npm:^7.22.15"
    babel-core: "npm:^7.0.0-bridge.0"
    chalk: "npm:^4.1.2"
    flow-parser: "npm:0.*"
    graceful-fs: "npm:^4.2.4"
    micromatch: "npm:^4.0.4"
    neo-async: "npm:^2.5.0"
    node-dir: "npm:^0.1.17"
    recast: "npm:^0.23.3"
    temp: "npm:^0.8.4"
    write-file-atomic: "npm:^2.3.0"
  peerDependencies:
    "@babel/preset-env": ^7.1.6
  peerDependenciesMeta:
    "@babel/preset-env":
      optional: true
  bin:
    jscodeshift: bin/jscodeshift.js
  checksum: 10c0/79afb059b9ca92712af02bdc8d6ff144de7aaf5e2cdcc6f6534e7a86a7347b0a278d9f4884f2c78dac424162a353aafff183a60e868f71132be2c5b5304aeeb8
  languageName: node
  linkType: hard

"jsdom@npm:^20.0.0":
  version: 20.0.3
  resolution: "jsdom@npm:20.0.3"
  dependencies:
    abab: "npm:^2.0.6"
    acorn: "npm:^8.8.1"
    acorn-globals: "npm:^7.0.0"
    cssom: "npm:^0.5.0"
    cssstyle: "npm:^2.3.0"
    data-urls: "npm:^3.0.2"
    decimal.js: "npm:^10.4.2"
    domexception: "npm:^4.0.0"
    escodegen: "npm:^2.0.0"
    form-data: "npm:^4.0.0"
    html-encoding-sniffer: "npm:^3.0.0"
    http-proxy-agent: "npm:^5.0.0"
    https-proxy-agent: "npm:^5.0.1"
    is-potential-custom-element-name: "npm:^1.0.1"
    nwsapi: "npm:^2.2.2"
    parse5: "npm:^7.1.1"
    saxes: "npm:^6.0.0"
    symbol-tree: "npm:^3.2.4"
    tough-cookie: "npm:^4.1.2"
    w3c-xmlserializer: "npm:^4.0.0"
    webidl-conversions: "npm:^7.0.0"
    whatwg-encoding: "npm:^2.0.0"
    whatwg-mimetype: "npm:^3.0.0"
    whatwg-url: "npm:^11.0.0"
    ws: "npm:^8.11.0"
    xml-name-validator: "npm:^4.0.0"
  peerDependencies:
    canvas: ^2.5.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10c0/b109073bb826a966db7828f46cb1d7371abecd30f182b143c52be5fe1ed84513bbbe995eb3d157241681fcd18331381e61e3dc004d4949f3a63bca02f6214902
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/ef22148f9e793180b14d8a145ee6f9f60f301abf443288117b4b6c53d0ecd58354898dc506ccbb553a5f7827965cd38bc5fb726575aae93c5e8915e2de8290e1
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: 10c0/2f1287a7c833e397c9ddd361a78638e828fc523038bb3441fd4fc144cfd2c6cd4963ffb9e207e648cf7b692600f1e1e524e965c32df5152120910e4903a47dcb
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10c0/140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10c0/71e30015d7f3d6dc1c316d6298047c8ef98a06d31ad064919976583eb61e1018a60a0067338f0f79cabc00d84af3fcc489bd48ce8a46ea165d9541ba17fb30c6
  languageName: node
  linkType: hard

"json-stable-stringify@npm:^1.0.2":
  version: 1.3.0
  resolution: "json-stable-stringify@npm:1.3.0"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    isarray: "npm:^2.0.5"
    jsonify: "npm:^0.0.1"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/8b3ff19e4c23c0ad591a49bc3a015d89a538db787d12fe9c4072e1d64d8cfa481f8c37719c629c3d84e848847617bf49f5fee894cf1d25959ab5b67e1c517f31
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jsonify@npm:^0.0.1":
  version: 0.0.1
  resolution: "jsonify@npm:0.0.1"
  checksum: 10c0/7f5499cdd59a0967ed35bda48b7cec43d850bbc8fb955cdd3a1717bb0efadbe300724d5646de765bb7a99fc1c3ab06eb80d93503c6faaf99b4ff50a3326692f6
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10c0/61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"klaw-sync@npm:^6.0.0":
  version: 6.0.0
  resolution: "klaw-sync@npm:6.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.11"
  checksum: 10c0/00d8e4c48d0d699b743b3b028e807295ea0b225caf6179f51029e19783a93ad8bb9bccde617d169659fbe99559d73fb35f796214de031d0023c26b906cecd70a
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10c0/cd3a0b8878e7d6d3799e54340efe3591ca787d9f95f109f28129bdd2915e37807bf8918bb295ab86afb8c82196beec5a1adcaf29042ce3f2bd932b038fe3aa4b
  languageName: node
  linkType: hard

"lan-network@npm:^0.1.6":
  version: 0.1.6
  resolution: "lan-network@npm:0.1.6"
  bin:
    lan-network: dist/lan-network-cli.js
  checksum: 10c0/5932a9d67c40fad53da5b383c4623dba700f1b6553d4aea740f5e1cad298a908f2a14dec940ac2e149b06deda73124fc9971e7dbaf162b7f9e077a2d59cd7b20
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 10c0/cd778ba3fbab0f4d0500b7e87d1f6e1f041507c56fdcd47e8256a3012c98aaee371d4c15e0a76e0386107af2d42e2b7466160a2d80688aaa03e66e49949f42df
  languageName: node
  linkType: hard

"lighthouse-logger@npm:^1.0.0":
  version: 1.4.2
  resolution: "lighthouse-logger@npm:1.4.2"
  dependencies:
    debug: "npm:^2.6.9"
    marky: "npm:^1.2.2"
  checksum: 10c0/090431db34e9ce01b03b2a03b39e998807a7a86214f2e8da2ba9588c36841caf4474f96ef1b2deaf9fe58f2e00f9f51618e0b98edecc2d8c9dfc13185bf0adc8
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-darwin-arm64@npm:1.27.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-darwin-arm64@npm:1.30.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-darwin-x64@npm:1.27.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-darwin-x64@npm:1.30.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-freebsd-x64@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-freebsd-x64@npm:1.27.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-freebsd-x64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-freebsd-x64@npm:1.30.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.27.0"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.30.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm64-gnu@npm:1.27.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm64-gnu@npm:1.30.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-arm64-musl@npm:1.27.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm64-musl@npm:1.30.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-x64-gnu@npm:1.27.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-x64-gnu@npm:1.30.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-linux-x64-musl@npm:1.27.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-x64-musl@npm:1.30.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-win32-arm64-msvc@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-win32-arm64-msvc@npm:1.27.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-win32-arm64-msvc@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-win32-arm64-msvc@npm:1.30.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.27.0":
  version: 1.27.0
  resolution: "lightningcss-win32-x64-msvc@npm:1.27.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-win32-x64-msvc@npm:1.30.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss@npm:^1.27.0":
  version: 1.30.1
  resolution: "lightningcss@npm:1.30.1"
  dependencies:
    detect-libc: "npm:^2.0.3"
    lightningcss-darwin-arm64: "npm:1.30.1"
    lightningcss-darwin-x64: "npm:1.30.1"
    lightningcss-freebsd-x64: "npm:1.30.1"
    lightningcss-linux-arm-gnueabihf: "npm:1.30.1"
    lightningcss-linux-arm64-gnu: "npm:1.30.1"
    lightningcss-linux-arm64-musl: "npm:1.30.1"
    lightningcss-linux-x64-gnu: "npm:1.30.1"
    lightningcss-linux-x64-musl: "npm:1.30.1"
    lightningcss-win32-arm64-msvc: "npm:1.30.1"
    lightningcss-win32-x64-msvc: "npm:1.30.1"
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-freebsd-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-arm64-msvc:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: 10c0/1e1ad908f3c68bf39d964a6735435a8dd5474fb2765076732d64a7b6aa2af1f084da65a9462443a9adfebf7dcfb02fb532fce1d78697f2a9de29c8f40f09aee3
  languageName: node
  linkType: hard

"lightningcss@npm:~1.27.0":
  version: 1.27.0
  resolution: "lightningcss@npm:1.27.0"
  dependencies:
    detect-libc: "npm:^1.0.3"
    lightningcss-darwin-arm64: "npm:1.27.0"
    lightningcss-darwin-x64: "npm:1.27.0"
    lightningcss-freebsd-x64: "npm:1.27.0"
    lightningcss-linux-arm-gnueabihf: "npm:1.27.0"
    lightningcss-linux-arm64-gnu: "npm:1.27.0"
    lightningcss-linux-arm64-musl: "npm:1.27.0"
    lightningcss-linux-x64-gnu: "npm:1.27.0"
    lightningcss-linux-x64-musl: "npm:1.27.0"
    lightningcss-win32-arm64-msvc: "npm:1.27.0"
    lightningcss-win32-x64-msvc: "npm:1.27.0"
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-freebsd-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-arm64-msvc:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: 10c0/5292b277ebbefdd952cb7b9ccd20dd2c185a7eae9b4393960386b7b8c4d644492a413a91d05ca9dcb72c775bbb8d79b235a3415d66410c47464039394d022109
  languageName: node
  linkType: hard

"lilconfig@npm:^3.0.0, lilconfig@npm:^3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 10c0/f5604e7240c5c275743561442fbc5abf2a84ad94da0f5adc71d25e31fa8483048de3dcedcb7a44112a942fed305fd75841cdf6c9681c7f640c63f1049e9a5dcc
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: "npm:^3.0.0"
    path-exists: "npm:^3.0.0"
  checksum: 10c0/3db394b7829a7fe2f4fbdd25d3c4689b85f003c318c5da4052c7e56eed697da8f1bce5294f685c69ff76e32cba7a33629d94396976f6d05fb7f4c755c5e2ae8b
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10c0/33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10c0/762998a63e095412b6099b8290903e0a8ddcb353ac6e2e0f2d7e7d03abd4275fe3c689d88960eb90b0dde4f177554d51a690f22a343932ecbc50a5d111849987
  languageName: node
  linkType: hard

"lodash.throttle@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.throttle@npm:4.1.1"
  checksum: 10c0/14628013e9e7f65ac904fc82fd8ecb0e55a9c4c2416434b1dd9cf64ae70a8937f0b15376a39a68248530adc64887ed0fe2b75204b2c9ec3eea1cb2d66ddd125d
  languageName: node
  linkType: hard

"lodash@npm:^4.17.19, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"lodash@npm:~2.4.1":
  version: 2.4.2
  resolution: "lodash@npm:2.4.2"
  checksum: 10c0/7c235180b20dc4b66cbf71d2347962798ad0c9669bb446a7098c22c8a37b3a63dc7880a71a91db314e7172de3c8cb5b1549942dfb8249d0b008ea5b66a067eb7
  languageName: node
  linkType: hard

"log-symbols@npm:^2.2.0":
  version: 2.2.0
  resolution: "log-symbols@npm:2.2.0"
  dependencies:
    chalk: "npm:^2.0.1"
  checksum: 10c0/574eb4205f54f0605021aa67ebb372c30ca64e8ddd439efeb8507af83c776dce789e83614e80059014d9e48dcc94c4b60cef2e85f0dc944eea27c799cec62353
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10c0/3d925e090315cf7dc1caa358e0477e186ffa23947740e4314a7429b6e62d72742e0bbe7536a5ae56d19d7618ce998aba05caca53c2902bd5742fdca5fc57fd7b
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"lucide-react-native@npm:^0.469.0":
  version: 0.469.0
  resolution: "lucide-react-native@npm:0.469.0"
  peerDependencies:
    react: ^16.5.1 || ^17.0.0 || ^18.0.0
    react-native: "*"
    react-native-svg: ^12.0.0 || ^13.0.0 || ^14.0.0 || ^15.0.0
  checksum: 10c0/19bdd60931a8db3dc274deaf8a0c5263c78c76d9d8ff2e0f57a8e371befbcf6a094bd06f063754180b57aaa13f09711c0cb7d6e3b5bd9d0dae6e6c883a1dd944
  languageName: node
  linkType: hard

"make-dir@npm:^2.0.0, make-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: "npm:^4.0.1"
    semver: "npm:^5.6.0"
  checksum: 10c0/ada869944d866229819735bee5548944caef560d7a8536ecbc6536edca28c72add47cc4f6fc39c54fb25d06b58da1f8994cf7d9df7dadea047064749efc085d8
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10c0/69b98a6c0b8e5c4fe9acb61608a9fbcfca1756d910f51e5dbe7a9e5cfb74fca9b8a0c8a0ffdf1294a740826c1ab4871d5bf3f62f72a3049e5eac6541ddffed68
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: "npm:1.0.5"
  checksum: 10c0/b0e6e599780ce6bab49cc413eba822f7d1f0dfebd1c103eaa3785c59e43e22c59018323cf9e1708f0ef5329e94a745d163fcbb6bff8e4c6742f9be9e86f3500c
  languageName: node
  linkType: hard

"marky@npm:^1.2.2":
  version: 1.3.0
  resolution: "marky@npm:1.3.0"
  checksum: 10c0/6619cdb132fdc4f7cd3e2bed6eebf81a38e50ff4b426bbfb354db68731e4adfebf35ebfd7c8e5a6e846cbf9b872588c4f76db25782caee8c1529ec9d483bf98b
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 10c0/67241f8708c1e665a061d2b042d2d243366e93e5bf1f917693007f6d55111588b952dcbfd3ea9c2d0969fb754aad81b30fdcfdcc24546495fc3b24336b28d4bd
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.28":
  version: 2.0.28
  resolution: "mdn-data@npm:2.0.28"
  checksum: 10c0/20000932bc4cd1cde9cba4e23f08cc4f816398af4c15ec81040ed25421d6bf07b5cf6b17095972577fb498988f40f4cb589e3169b9357bb436a12d8e07e5ea7b
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.30":
  version: 2.0.30
  resolution: "mdn-data@npm:2.0.30"
  checksum: 10c0/a2c472ea16cee3911ae742593715aa4c634eb3d4b9f1e6ada0902aa90df13dcbb7285d19435f3ff213ebaa3b2e0c0265c1eb0e3fb278fda7f8919f046a410cd9
  languageName: node
  linkType: hard

"memoize-one@npm:^5.0.0":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: 10c0/fd22dbe9a978a2b4f30d6a491fc02fb90792432ad0dab840dc96c1734d2bd7c9cdeb6a26130ec60507eb43230559523615873168bcbe8fafab221c30b11d54c1
  languageName: node
  linkType: hard

"memoize-one@npm:^6.0.0":
  version: 6.0.0
  resolution: "memoize-one@npm:6.0.0"
  checksum: 10c0/45c88e064fd715166619af72e8cf8a7a17224d6edf61f7a8633d740ed8c8c0558a4373876c9b8ffc5518c2b65a960266adf403cc215cb1e90f7e262b58991f54
  languageName: node
  linkType: hard

"memory-pager@npm:^1.0.2":
  version: 1.5.0
  resolution: "memory-pager@npm:1.5.0"
  checksum: 10c0/2596e80c99fee24f05bd8a20cde2ee899012c996f4ec361ac76ed6f009f34149d733ac6f76880106ccd6a66d062ad439357578d383d429df66ba1278f68806e9
  languageName: node
  linkType: hard

"merge-options@npm:^3.0.4":
  version: 3.0.4
  resolution: "merge-options@npm:3.0.4"
  dependencies:
    is-plain-obj: "npm:^2.1.0"
  checksum: 10c0/02b5891ceef09b0b497b5a0154c37a71784e68ed71b14748f6fd4258ffd3fe4ecd5cb0fd6f7cae3954fd11e7686c5cb64486daffa63c2793bbe8b614b61c7055
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"metro-babel-transformer@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-babel-transformer@npm:0.82.3"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    flow-enums-runtime: "npm:^0.0.6"
    hermes-parser: "npm:0.28.1"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/8e961985dd36fe5d529943fae8aeed27ac3ec2fb63dbdd1343252da9fbcce3763fd92364b48e12465c7b53b9edc251bb2390d47686d2e8f1e683c21446300229
  languageName: node
  linkType: hard

"metro-cache-key@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-cache-key@npm:0.82.3"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/2d6e01d82773d6dcd220fecf22f202d4b9ebf368d7676295d931d10ffa2cb1e822ddbd914bf1648d65b2aee2f8e38269a471f1a105723c9d306c577d08302552
  languageName: node
  linkType: hard

"metro-cache@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-cache@npm:0.82.3"
  dependencies:
    exponential-backoff: "npm:^3.1.1"
    flow-enums-runtime: "npm:^0.0.6"
    https-proxy-agent: "npm:^7.0.5"
    metro-core: "npm:0.82.3"
  checksum: 10c0/8520ef1ae227ce5f82835396b5bacf3939ea0519a1d319e63c7f2582b4727f548d084e2947e505aba9e6168dd0744ed381443225f23cb7a5d609b386bd015e8c
  languageName: node
  linkType: hard

"metro-config@npm:0.82.3, metro-config@npm:^0.82.0":
  version: 0.82.3
  resolution: "metro-config@npm:0.82.3"
  dependencies:
    connect: "npm:^3.6.5"
    cosmiconfig: "npm:^5.0.5"
    flow-enums-runtime: "npm:^0.0.6"
    jest-validate: "npm:^29.7.0"
    metro: "npm:0.82.3"
    metro-cache: "npm:0.82.3"
    metro-core: "npm:0.82.3"
    metro-runtime: "npm:0.82.3"
  checksum: 10c0/1d3432b6172a9b5025d58f8f569c2ba71fecfac9be7d0eada3cdf0b8c3f1a517a4979f3de8b6d703c7904ac15e4c137821f08e2cda01470240611e320baea28f
  languageName: node
  linkType: hard

"metro-core@npm:0.82.3, metro-core@npm:^0.82.0":
  version: 0.82.3
  resolution: "metro-core@npm:0.82.3"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    lodash.throttle: "npm:^4.1.1"
    metro-resolver: "npm:0.82.3"
  checksum: 10c0/d38ce831c060653868313160b0a54c2432d7d1b6d20c415dce254a386342262ae08a3a69bedae4644fc7f349b8e8742dc27a14b79917dd351ceb1b7208c70519
  languageName: node
  linkType: hard

"metro-file-map@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-file-map@npm:0.82.3"
  dependencies:
    debug: "npm:^4.4.0"
    fb-watchman: "npm:^2.0.0"
    flow-enums-runtime: "npm:^0.0.6"
    graceful-fs: "npm:^4.2.4"
    invariant: "npm:^2.2.4"
    jest-worker: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    nullthrows: "npm:^1.1.1"
    walker: "npm:^1.0.7"
  checksum: 10c0/9a7e60cdc14d9655251360cd7d2675c00ea2b01aa0bbc9e5e60eb096a42958c38f565eb008e3a476d2832876deb539a313602a5b61382973e5a808251a784ffe
  languageName: node
  linkType: hard

"metro-minify-terser@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-minify-terser@npm:0.82.3"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    terser: "npm:^5.15.0"
  checksum: 10c0/0b6220ee516407c69425850782ff2e7e035d6a8fa88fed04d50e8c83de980f2afba392f35a5317b60e1ae8460011d57fa2cf08ccf700f81b9125ccc165fd55e3
  languageName: node
  linkType: hard

"metro-resolver@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-resolver@npm:0.82.3"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/31916164e0ec914da322a58c254c4b36f703f32679271829c7785a802f9572a3547edc82dd6beba33e525f1697ed4c8c7378a03e04b7319edf35820fe171f3de
  languageName: node
  linkType: hard

"metro-runtime@npm:0.82.3, metro-runtime@npm:^0.82.0":
  version: 0.82.3
  resolution: "metro-runtime@npm:0.82.3"
  dependencies:
    "@babel/runtime": "npm:^7.25.0"
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/9df7bd545095a165ad073aabe415140773ebe6ee7bb276300f69054b0629f7b4cec58845e180156d6bf96175f32594ab5dbbcb58d06a5c31a8a65be723acc637
  languageName: node
  linkType: hard

"metro-source-map@npm:0.82.3, metro-source-map@npm:^0.82.0":
  version: 0.82.3
  resolution: "metro-source-map@npm:0.82.3"
  dependencies:
    "@babel/traverse": "npm:^7.25.3"
    "@babel/traverse--for-generate-function-map": "npm:@babel/traverse@^7.25.3"
    "@babel/types": "npm:^7.25.2"
    flow-enums-runtime: "npm:^0.0.6"
    invariant: "npm:^2.2.4"
    metro-symbolicate: "npm:0.82.3"
    nullthrows: "npm:^1.1.1"
    ob1: "npm:0.82.3"
    source-map: "npm:^0.5.6"
    vlq: "npm:^1.0.0"
  checksum: 10c0/f893eae36ffd80e266075396e8d1bcc4f18a6e4b123bb7da4e353cf317bd083290aff632e950cd8d97f0acfc7312572b2008d9fb833e2e9e4de8fbd7f14815ed
  languageName: node
  linkType: hard

"metro-symbolicate@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-symbolicate@npm:0.82.3"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
    invariant: "npm:^2.2.4"
    metro-source-map: "npm:0.82.3"
    nullthrows: "npm:^1.1.1"
    source-map: "npm:^0.5.6"
    vlq: "npm:^1.0.0"
  bin:
    metro-symbolicate: src/index.js
  checksum: 10c0/0f039cbfbd2e5a5dec58919e6ec0dead4cd5ad6e51a518515147f451eadc5336ddee7d96d9754a189773d2460ded0be6be9850210ea402ddd2924653e7ba8740
  languageName: node
  linkType: hard

"metro-transform-plugins@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-transform-plugins@npm:0.82.3"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@babel/generator": "npm:^7.25.0"
    "@babel/template": "npm:^7.25.0"
    "@babel/traverse": "npm:^7.25.3"
    flow-enums-runtime: "npm:^0.0.6"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/13c334a3d7c45efcddfddf0066f0ed63b580612daa0120407c96a1ec4b106d9ac6f3d5d5a197e5692a0eddf93011fb345e10fce6a077177309c6bd8ab17d9de1
  languageName: node
  linkType: hard

"metro-transform-worker@npm:0.82.3":
  version: 0.82.3
  resolution: "metro-transform-worker@npm:0.82.3"
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@babel/generator": "npm:^7.25.0"
    "@babel/parser": "npm:^7.25.3"
    "@babel/types": "npm:^7.25.2"
    flow-enums-runtime: "npm:^0.0.6"
    metro: "npm:0.82.3"
    metro-babel-transformer: "npm:0.82.3"
    metro-cache: "npm:0.82.3"
    metro-cache-key: "npm:0.82.3"
    metro-minify-terser: "npm:0.82.3"
    metro-source-map: "npm:0.82.3"
    metro-transform-plugins: "npm:0.82.3"
    nullthrows: "npm:^1.1.1"
  checksum: 10c0/e006083f1d12525e105481f58f66a4e7c199d958b13d34d57a9fb75b93ff0c70970a865f2c2a6dbc22c9714662c3acde3df84158e2e5bdd4cd40dd0c398b1fb1
  languageName: node
  linkType: hard

"metro@npm:0.82.3, metro@npm:^0.82.0":
  version: 0.82.3
  resolution: "metro@npm:0.82.3"
  dependencies:
    "@babel/code-frame": "npm:^7.24.7"
    "@babel/core": "npm:^7.25.2"
    "@babel/generator": "npm:^7.25.0"
    "@babel/parser": "npm:^7.25.3"
    "@babel/template": "npm:^7.25.0"
    "@babel/traverse": "npm:^7.25.3"
    "@babel/types": "npm:^7.25.2"
    accepts: "npm:^1.3.7"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^2.0.0"
    connect: "npm:^3.6.5"
    debug: "npm:^4.4.0"
    error-stack-parser: "npm:^2.0.6"
    flow-enums-runtime: "npm:^0.0.6"
    graceful-fs: "npm:^4.2.4"
    hermes-parser: "npm:0.28.1"
    image-size: "npm:^1.0.2"
    invariant: "npm:^2.2.4"
    jest-worker: "npm:^29.7.0"
    jsc-safe-url: "npm:^0.2.2"
    lodash.throttle: "npm:^4.1.1"
    metro-babel-transformer: "npm:0.82.3"
    metro-cache: "npm:0.82.3"
    metro-cache-key: "npm:0.82.3"
    metro-config: "npm:0.82.3"
    metro-core: "npm:0.82.3"
    metro-file-map: "npm:0.82.3"
    metro-resolver: "npm:0.82.3"
    metro-runtime: "npm:0.82.3"
    metro-source-map: "npm:0.82.3"
    metro-symbolicate: "npm:0.82.3"
    metro-transform-plugins: "npm:0.82.3"
    metro-transform-worker: "npm:0.82.3"
    mime-types: "npm:^2.1.27"
    nullthrows: "npm:^1.1.1"
    serialize-error: "npm:^2.1.0"
    source-map: "npm:^0.5.6"
    throat: "npm:^5.0.0"
    ws: "npm:^7.5.10"
    yargs: "npm:^17.6.2"
  bin:
    metro: src/cli.js
  checksum: 10c0/619e8dfc27ca2350c319ad15e8449613dfed8ca4e76841da9f84743ab06f4c8143c25762d10bbc80af6fa1b0d202e757f0e4c9f1c4ca2b9a02598ea91339abe4
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: 10c0/8d907917bc2a90fa2df842cdf5dfeaf509adc15fe0531e07bb2f6ab15992416479015828d6a74200041c492e42cce3ebf78e5ce714388a0a538ea9c53eece284
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.27, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: 10c0/ad55214aec6094c0af4c0beec1a13787556f8116ed88807cf3f05828500f21f93a9482326bcd5a077ae91e3e8795b4e76b5b4c8bb12237ff0e4043a365516cba
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.2, minimatch@npm:^3.0.4, minimatch@npm:^3.1.1":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^8.0.2":
  version: 8.0.4
  resolution: "minimatch@npm:8.0.4"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/a0a394c356dd5b4cb7f821720841a82fa6f07c9c562c5b716909d1b6ec5e56a7e4c4b5029da26dd256b7d2b3a3f38cbf9ddd8680e887b9b5282b09c05501c1ca
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.0, minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minimongo-cache@npm:0.0.25":
  version: 0.0.25
  resolution: "minimongo-cache@npm:0.0.25"
  dependencies:
    eventemitter3: "npm:^1.1.0"
    lodash: "npm:~2.4.1"
  checksum: 10c0/75dd9938f52b4f6f1c2a5cd41bda2d71293609d9c7f6c2bdce36f5037963e3ded67e0337736d316f5f116292d19a20f3b1e37bd7897d36da20224b17b378da9d
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^4.2.4":
  version: 4.2.8
  resolution: "minipass@npm:4.2.8"
  checksum: 10c0/4ea76b030d97079f4429d6e8a8affd90baf1b6a1898977c8ccce4701c5a2ba2792e033abc6709373f25c2c4d4d95440d9d5e9464b46b7b76ca44d2ce26d939ce
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"moment@npm:^2.30.1":
  version: 2.30.1
  resolution: "moment@npm:2.30.1"
  checksum: 10c0/865e4279418c6de666fca7786607705fd0189d8a7b7624e2e56be99290ac846f90878a6f602e34b4e0455c549b85385b1baf9966845962b313699e7cb847543a
  languageName: node
  linkType: hard

"mongodb-connection-string-url@npm:^2.6.0":
  version: 2.6.0
  resolution: "mongodb-connection-string-url@npm:2.6.0"
  dependencies:
    "@types/whatwg-url": "npm:^8.2.1"
    whatwg-url: "npm:^11.0.0"
  checksum: 10c0/1e26b045063f4b3eb58fe445bfaf4e1ac3b9b9ceebc30c6deef5e769323cadb00e62cbe1d26a15fda457643d40a9ef9a24a94a1e993addb9261d87cad1fbf0ae
  languageName: node
  linkType: hard

"mongodb@npm:^4.3.1":
  version: 4.17.2
  resolution: "mongodb@npm:4.17.2"
  dependencies:
    "@aws-sdk/credential-providers": "npm:^3.186.0"
    "@mongodb-js/saslprep": "npm:^1.1.0"
    bson: "npm:^4.7.2"
    mongodb-connection-string-url: "npm:^2.6.0"
    socks: "npm:^2.7.1"
  dependenciesMeta:
    "@aws-sdk/credential-providers":
      optional: true
    "@mongodb-js/saslprep":
      optional: true
  checksum: 10c0/5434d995dc2173d78218ca858555c132a8354e30ac3e7ca4852ccaf5e8d3fdab4dc51a16db2a2b4096d9b8e6df799645eb9ffb0fb54cb814e4493996a4cd2b05
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"myuse@workspace:.":
  version: 0.0.0-use.local
  resolution: "myuse@workspace:."
  dependencies:
    "@babel/core": "npm:^7.25.2"
    "@expo/html-elements": "npm:0.4.2"
    "@expo/vector-icons": "npm:^14.0.2"
    "@gluestack-ui/actionsheet": "npm:^0.2.52"
    "@gluestack-ui/alert-dialog": "npm:^0.1.38"
    "@gluestack-ui/icon": "npm:^0.1.26"
    "@gluestack-ui/nativewind-utils": "npm:^1.0.26"
    "@gluestack-ui/overlay": "npm:^0.1.22"
    "@gluestack-ui/toast": "npm:^1.0.9"
    "@gorhom/bottom-sheet": "npm:^5"
    "@hookform/resolvers": "npm:^3.10.0"
    "@legendapp/motion": "npm:^2.4.0"
    "@meteorrn/core": "npm:^2.8.1"
    "@react-native-async-storage/async-storage": "npm:2.1.2"
    "@react-native-community/datetimepicker": "npm:8.3.0"
    "@react-native-community/netinfo": "npm:^11.4.1"
    "@react-native-google-signin/google-signin": "npm:^13.1.0"
    "@react-navigation/native": "npm:^7.0.14"
    "@shopify/react-native-skia": "npm:v2.0.0-next.4"
    "@tanstack/eslint-plugin-query": "npm:4"
    "@tanstack/react-query": "npm:^5.53.1"
    "@trivago/prettier-plugin-sort-imports": "npm:^5.2.1"
    "@types/meteor": "npm:^2.9.8"
    "@types/react": "npm:~19.0.10"
    "@types/styled-components-react-native": "npm:^5.2.5"
    babel-plugin-module-resolver: "npm:^5.0.2"
    base-64: "npm:^1.0.0"
    ddp-client: "npm:^0.1.2"
    expo: "npm:^53.0.9"
    expo-apple-authentication: "npm:~7.2.4"
    expo-file-system: "npm:~18.1.10"
    expo-font: "npm:~13.3.1"
    expo-haptics: "npm:^14.0.1"
    expo-image: "npm:~2.1.7"
    expo-image-picker: "npm:^16.0.6"
    expo-linear-gradient: "npm:^14.1.4"
    expo-linking: "npm:~7.1.5"
    expo-local-authentication: "npm:~16.0.4"
    expo-location: "npm:~18.1.5"
    expo-media-library: "npm:~17.1.6"
    expo-router: "npm:~5.0.7"
    expo-secure-store: "npm:~14.2.3"
    expo-sharing: "npm:~13.1.5"
    expo-splash-screen: "npm:~0.30.8"
    expo-status-bar: "npm:~2.2.3"
    expo-system-ui: "npm:~5.0.7"
    expo-web-browser: "npm:^14.1.6"
    jest: "npm:^29.2.1"
    jest-expo: "npm:~53.0.5"
    jscodeshift: "npm:0.15.2"
    lucide-react-native: "npm:^0.469.0"
    moment: "npm:^2.30.1"
    nativewind: "npm:^4.1.23"
    react: "npm:19.0.0"
    react-dom: "npm:19.0.0"
    react-hook-form: "npm:^7.54.2"
    react-native: "npm:0.79.2"
    react-native-autocomplete-input: "npm:^5.5.6"
    react-native-css-interop: "npm:^0.1.22"
    react-native-draggable-flatlist: "npm:^4.0.1"
    react-native-gesture-handler: "npm:~2.24.0"
    react-native-get-random-values: "npm:^1.11.0"
    react-native-google-places-autocomplete: "npm:^2.5.7"
    react-native-keyboard-aware-scroll-view: "npm:^0.9.5"
    react-native-modal: "npm:14.0.0-rc.1"
    react-native-paper: "npm:^5.14.5"
    react-native-reanimated: "npm:~3.17.4"
    react-native-safe-area-context: "npm:5.4.0"
    react-native-screens: "npm:~4.10.0"
    react-native-svg: "npm:15.11.2"
    react-native-svg-transformer: "npm:^1.5.0"
    react-native-ui-datepicker: "npm:^3.1.2"
    react-native-uuid: "npm:^2.0.3"
    react-native-view-shot: "npm:~4.0.3"
    react-native-web: "npm:^0.20.0"
    react-test-renderer: "npm:18.3.1"
    styled-components: "npm:^6.1.14"
    tailwindcss: "npm:3.4.17"
    typescript: "npm:~5.8.3"
    yup: "npm:^1.6.1"
  languageName: unknown
  linkType: soft

"mz@npm:^2.7.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: "npm:^1.0.0"
    object-assign: "npm:^4.0.1"
    thenify-all: "npm:^1.0.0"
  checksum: 10c0/103114e93f87362f0b56ab5b2e7245051ad0276b646e3902c98397d18bb8f4a77f2ea4a2c9d3ad516034ea3a56553b60d3f5f78220001ca4c404bd711bd0af39
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.1, nanoid@npm:^3.3.11, nanoid@npm:^3.3.7, nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"nativewind@npm:^4.1.23":
  version: 4.1.23
  resolution: "nativewind@npm:4.1.23"
  dependencies:
    comment-json: "npm:^4.2.5"
    debug: "npm:^4.3.7"
    react-native-css-interop: "npm:0.1.22"
  peerDependencies:
    tailwindcss: ">3.3.0"
  checksum: 10c0/135c9a07afdc59d9f9f5a9d0d80c97766694c39243dd0eb21b01001814447792d7eca358c276b89f62d4d060c30a1de9732a0bb676590ac3a194da15ef3dea5f
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10c0/3e677139c7fb7628a6f36335bf11a885a62c21d5390204590a1a214a5631fcbe5ea74ef6a610b60afe84b4d975cbe0566a23f20ee17c77c73e74b80032108dea
  languageName: node
  linkType: hard

"neo-async@npm:^2.5.0, neo-async@npm:^2.6.1":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10c0/c2f5a604a54a8ec5438a342e1f356dff4bc33ccccdb6dc668d94fe8e5eccfc9d2c2eea6064b0967a767ba63b33763f51ccf2cd2441b461a7322656c1f06b3f5d
  languageName: node
  linkType: hard

"nested-error-stacks@npm:~2.0.1":
  version: 2.0.1
  resolution: "nested-error-stacks@npm:2.0.1"
  checksum: 10c0/125049632bc3ca2252e994ca07f27d795c0e6decc4077f0f4163348d30d7cb95409ceff6184284c95396aa5ea8ff5010673063db7674058b966b4f0228d4981c
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: "npm:^2.0.2"
    tslib: "npm:^2.0.3"
  checksum: 10c0/8ef545f0b3f8677c848f86ecbd42ca0ff3cd9dd71c158527b344c69ba14710d816d8489c746b6ca225e7b615108938a0bda0a54706f8c255933703ac1cf8e703
  languageName: node
  linkType: hard

"node-dir@npm:^0.1.17":
  version: 0.1.17
  resolution: "node-dir@npm:0.1.17"
  dependencies:
    minimatch: "npm:^3.0.2"
  checksum: 10c0/16222e871708c405079ff8122d4a7e1d522c5b90fc8f12b3112140af871cfc70128c376e845dcd0044c625db0d2efebd2d852414599d240564db61d53402b4c1
  languageName: node
  linkType: hard

"node-fetch@npm:^2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-forge@npm:^1.2.1, node-forge@npm:^1.3.1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 10c0/e882819b251a4321f9fc1d67c85d1501d3004b4ee889af822fd07f64de3d1a8e272ff00b689570af0465d65d6bf5074df9c76e900e0aff23e60b847f2a46fbe8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/bd8d8c76b06be761239b0c8680f655f6a6e90b48e44d43415b11c16f7e8c15be346fba0cbf71588c7cdfb52c419d928a7d3db353afc1d952d19756237d8f10b9
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: 10c0/a6a4d8369e2f2720e9c645255ffde909c0fbd41c92ea92a5607fc17055955daac99c1ff589d421eee12a0d24e99f7bfc2aabfeb1a4c14742f6c099a51863f31a
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"npm-package-arg@npm:^11.0.0":
  version: 11.0.3
  resolution: "npm-package-arg@npm:11.0.3"
  dependencies:
    hosted-git-info: "npm:^7.0.0"
    proc-log: "npm:^4.0.0"
    semver: "npm:^7.3.5"
    validate-npm-package-name: "npm:^5.0.0"
  checksum: 10c0/e18333485e05c3a8774f4b5701ef74f4799533e650b70a68ca8dd697666c9a8d46932cb765fc593edce299521033bd4025a40323d5240cea8a393c784c0c285a
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10c0/5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"nullthrows@npm:^1.1.1":
  version: 1.1.1
  resolution: "nullthrows@npm:1.1.1"
  checksum: 10c0/56f34bd7c3dcb3bd23481a277fa22918120459d3e9d95ca72976c72e9cac33a97483f0b95fc420e2eb546b9fe6db398273aba9a938650cdb8c98ee8f159dcb30
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.2":
  version: 2.2.20
  resolution: "nwsapi@npm:2.2.20"
  checksum: 10c0/07f4dafa3186aef7c007863e90acd4342a34ba9d44b22f14f644fdb311f6086887e21c2fc15efaa826c2bc39ab2bc841364a1a630e7c87e0cb723ba59d729297
  languageName: node
  linkType: hard

"ob1@npm:0.82.3":
  version: 0.82.3
  resolution: "ob1@npm:0.82.3"
  dependencies:
    flow-enums-runtime: "npm:^0.0.6"
  checksum: 10c0/ec182cde8582bdd6931452a8823a343c89594053851da7317a8935d6caf27935c95d0f70d647a88784eef500d1fb2807f33c92445a3ba4fab1dbb89baa8acd52
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-hash@npm:^3.0.0":
  version: 3.0.0
  resolution: "object-hash@npm:3.0.0"
  checksum: 10c0/a06844537107b960c1c8b96cd2ac8592a265186bfa0f6ccafe0d34eabdb526f6fa81da1f37c43df7ed13b12a4ae3457a16071603bcd39d8beddb5f08c37b0f47
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/c904f9e518b11941eb60279a3cbfaf1289bd0001f600a950255b1dede9fe3df8cd74f38483550b3bb9485165166acb5db500c3b4c4337aec2815c88c96fcc2ea
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 10c0/f649e65c197bf31505a4c0444875db0258e198292f34b884d73c2f751e91792ef96bb5cf89aa0f4fecc2e4dc662461dda606b1274b0e564f539cae5d2f5fc32f
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^2.0.0":
  version: 2.0.1
  resolution: "onetime@npm:2.0.1"
  dependencies:
    mimic-fn: "npm:^1.0.0"
  checksum: 10c0/b4e44a8c34e70e02251bfb578a6e26d6de6eedbed106cd78211d2fd64d28b6281d54924696554e4e966559644243753ac5df73c87f283b0927533d3315696215
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"open@npm:^7.0.3, open@npm:^7.4.2":
  version: 7.4.2
  resolution: "open@npm:7.4.2"
  dependencies:
    is-docker: "npm:^2.0.0"
    is-wsl: "npm:^2.1.1"
  checksum: 10c0/77573a6a68f7364f3a19a4c80492712720746b63680ee304555112605ead196afe91052bd3c3d165efdf4e9d04d255e87de0d0a77acec11ef47fd5261251813f
  languageName: node
  linkType: hard

"open@npm:^8.0.4":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/bb6b3a58401dacdb0aad14360626faf3fb7fba4b77816b373495988b724fb48941cad80c1b65d62bb31a17609b2cd91c41a181602caea597ca80dfbcc27e84c9
  languageName: node
  linkType: hard

"ora@npm:^3.4.0":
  version: 3.4.0
  resolution: "ora@npm:3.4.0"
  dependencies:
    chalk: "npm:^2.4.2"
    cli-cursor: "npm:^2.1.0"
    cli-spinners: "npm:^2.0.0"
    log-symbols: "npm:^2.2.0"
    strip-ansi: "npm:^5.2.0"
    wcwidth: "npm:^1.0.1"
  checksum: 10c0/04cb375f222c36a16a95e6c39c473644a99a42fc34d35c37507cb836ea0a71f4d831fcd53198a460869114b2730891d63cc1047304afe5ddb078974d468edfb1
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 10c0/f438450224f8e2687605a8dd318f0db694b6293c5d835ae509a69e97c8de38b6994645337e5577f5001115470414638978cc49da1cdcc25106dad8738dc69990
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0, p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10c0/8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2, p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: "npm:^2.0.0"
  checksum: 10c0/7b7f06f718f19e989ce6280ed4396fb3c34dabdee0df948376483032f9d5ec22fdf7077ec942143a75827bb85b11da72016497fc10dac1106c837ed593969ee8
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10c0/1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10c0/c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: "npm:^1.3.1"
    json-parse-better-errors: "npm:^1.0.1"
  checksum: 10c0/8d80790b772ccb1bcea4e09e2697555e519d83d04a77c2b4237389b813f82898943a93ffff7d0d2406203bdd0c30dcf95b1661e3a53f83d0e417f053957bef32
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10c0/77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"parse-png@npm:^2.1.0":
  version: 2.1.0
  resolution: "parse-png@npm:2.1.0"
  dependencies:
    pngjs: "npm:^3.3.0"
  checksum: 10c0/5157a8bbb976ae1ca990fc53c7014d42aac0967cb30e2daf36c3fef1876c8db0d551e695400c904f33c5c5add76a572c65b5044721d62417d8cc7abe4c4ffa41
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0, parse5@npm:^7.1.1":
  version: 7.3.0
  resolution: "parse5@npm:7.3.0"
  dependencies:
    entities: "npm:^6.0.0"
  checksum: 10c0/7fd2e4e247e85241d6f2a464d0085eed599a26d7b0a5233790c49f53473232eb85350e8133344d9b3fd58b89339e7ad7270fe1f89d28abe50674ec97b87f80b5
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"patch-package@npm:8.0.0":
  version: 8.0.0
  resolution: "patch-package@npm:8.0.0"
  dependencies:
    "@yarnpkg/lockfile": "npm:^1.1.0"
    chalk: "npm:^4.1.2"
    ci-info: "npm:^3.7.0"
    cross-spawn: "npm:^7.0.3"
    find-yarn-workspace-root: "npm:^2.0.0"
    fs-extra: "npm:^9.0.0"
    json-stable-stringify: "npm:^1.0.2"
    klaw-sync: "npm:^6.0.0"
    minimist: "npm:^1.2.6"
    open: "npm:^7.4.2"
    rimraf: "npm:^2.6.3"
    semver: "npm:^7.5.3"
    slash: "npm:^2.0.0"
    tmp: "npm:^0.0.33"
    yaml: "npm:^2.2.2"
  bin:
    patch-package: index.js
  checksum: 10c0/690eab0537e953a3fd7d32bb23f0e82f97cd448f8244c3227ed55933611a126f9476397325c06ad2c11d881a19b427a02bd1881bee78d89f1731373fc4fe0fee
  languageName: node
  linkType: hard

"path-dirname@npm:^1.0.2":
  version: 1.0.2
  resolution: "path-dirname@npm:1.0.2"
  checksum: 10c0/71e59be2bada7c91f62b976245fd421b7cb01fde3207fe53a82d8880621ad04fd8b434e628c9cf4e796259fc168a107d77cd56837725267c5b2c58cefe2c4e1b
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 10c0/17d6a5664bc0a11d48e2b2127d28a0e58822c6740bde30403f08013da599182289c56518bec89407e3f31d3c2b6b296a4220bc3f867f0911fee6952208b04167
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.5, path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1, path-scurry@npm:^1.6.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^3.0.1":
  version: 3.0.1
  resolution: "picomatch@npm:3.0.1"
  checksum: 10c0/70ec738569f1864658378b7abdab8939d15dae0718c1df994eae3346fd33daf6a3c1ff4e0c1a0cd1e2c0319130985b63a2cff34d192f2f2acbb78aca76111736
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 10c0/551ff8ab830b1052633f59cb8adc9ae8407a436e06b4a9718bcb27dc5844b83d535c3a8512b388b6062af65a98c49bdc0dd523d8b2617b188f7c8fee457158dc
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 10c0/6f9d404b0d47a965437403c9b90eca8bb2536407f03de165940e62e72c8c8b75adda5516c6b9b23675a5877cc0bcac6bdfb0ef0e39414cd2476d5495da40e7cf
  languageName: node
  linkType: hard

"pirates@npm:^4.0.1, pirates@npm:^4.0.4, pirates@npm:^4.0.6":
  version: 4.0.7
  resolution: "pirates@npm:4.0.7"
  checksum: 10c0/a51f108dd811beb779d58a76864bbd49e239fa40c7984cd11596c75a121a8cc789f1c8971d8bb15f0dbf9d48b76c05bb62fcbce840f89b688c0fa64b37e8478a
  languageName: node
  linkType: hard

"pkg-dir@npm:^3.0.0":
  version: 3.0.0
  resolution: "pkg-dir@npm:3.0.0"
  dependencies:
    find-up: "npm:^3.0.0"
  checksum: 10c0/902a3d0c1f8ac43b1795fa1ba6ffeb37dfd53c91469e969790f6ed5e29ff2bdc50b63ba6115dc056d2efb4a040aa2446d512b3804bdafdf302f734fb3ec21847
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: "npm:^4.0.0"
  checksum: 10c0/c56bda7769e04907a88423feb320babaed0711af8c436ce3e56763ab1021ba107c7b0cafb11cde7529f669cfc22bffcaebffb573645cbd63842ea9fb17cd7728
  languageName: node
  linkType: hard

"pkg-up@npm:^3.1.0":
  version: 3.1.0
  resolution: "pkg-up@npm:3.1.0"
  dependencies:
    find-up: "npm:^3.0.0"
  checksum: 10c0/ecb60e1f8e1f611c0bdf1a0b6a474d6dfb51185567dc6f29cdef37c8d480ecba5362e006606bb290519bbb6f49526c403fabea93c3090c20368d98bb90c999ab
  languageName: node
  linkType: hard

"plist@npm:^3.0.5":
  version: 3.1.0
  resolution: "plist@npm:3.1.0"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.8"
    base64-js: "npm:^1.5.1"
    xmlbuilder: "npm:^15.1.1"
  checksum: 10c0/db19ba50faafc4103df8e79bcd6b08004a56db2a9dd30b3e5c8b0ef30398ef44344a674e594d012c8fc39e539a2b72cb58c60a76b4b4401cbbc7c8f6b028d93d
  languageName: node
  linkType: hard

"pngjs@npm:^3.3.0":
  version: 3.4.0
  resolution: "pngjs@npm:3.4.0"
  checksum: 10c0/88ee73e2ad3f736e0b2573722309eb80bd2aa28916f0862379b4fd0f904751b4f61bb6bd1ecd7d4242d331f2b5c28c13309dd4b7d89a9b78306e35122fdc5011
  languageName: node
  linkType: hard

"postcss-import@npm:^15.1.0":
  version: 15.1.0
  resolution: "postcss-import@npm:15.1.0"
  dependencies:
    postcss-value-parser: "npm:^4.0.0"
    read-cache: "npm:^1.0.0"
    resolve: "npm:^1.1.7"
  peerDependencies:
    postcss: ^8.0.0
  checksum: 10c0/518aee5c83ea6940e890b0be675a2588db68b2582319f48c3b4e06535a50ea6ee45f7e63e4309f8754473245c47a0372632378d1d73d901310f295a92f26f17b
  languageName: node
  linkType: hard

"postcss-js@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-js@npm:4.0.1"
  dependencies:
    camelcase-css: "npm:^2.0.1"
  peerDependencies:
    postcss: ^8.4.21
  checksum: 10c0/af35d55cb873b0797d3b42529514f5318f447b134541844285c9ac31a17497297eb72296902967911bb737a75163441695737300ce2794e3bd8c70c13a3b106e
  languageName: node
  linkType: hard

"postcss-load-config@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-load-config@npm:4.0.2"
  dependencies:
    lilconfig: "npm:^3.0.0"
    yaml: "npm:^2.3.4"
  peerDependencies:
    postcss: ">=8.0.9"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    postcss:
      optional: true
    ts-node:
      optional: true
  checksum: 10c0/3d7939acb3570b0e4b4740e483d6e555a3e2de815219cb8a3c8fc03f575a6bde667443aa93369c0be390af845cb84471bf623e24af833260de3a105b78d42519
  languageName: node
  linkType: hard

"postcss-nested@npm:^6.2.0":
  version: 6.2.0
  resolution: "postcss-nested@npm:6.2.0"
  dependencies:
    postcss-selector-parser: "npm:^6.1.1"
  peerDependencies:
    postcss: ^8.2.14
  checksum: 10c0/7f9c3f2d764191a39364cbdcec350f26a312431a569c9ef17408021424726b0d67995ff5288405e3724bb7152a4c92f73c027e580ec91e798800ed3c52e2bc6e
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.1.1, postcss-selector-parser@npm:^6.1.2":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10c0/523196a6bd8cf660bdf537ad95abd79e546d54180f9afb165a4ab3e651ac705d0f8b8ce6b3164fb9e3279ce482c5f751a69eb2d3a1e8eb0fd5e82294fb3ef13e
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.0.2, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10c0/f4142a4f56565f77c1831168e04e3effd9ffcc5aebaf0f538eee4b2d465adfd4b85a44257bb48418202a63806a7da7fe9f56c330aebb3cac898e46b4cbf49161
  languageName: node
  linkType: hard

"postcss@npm:8.4.49, postcss@npm:~8.4.32":
  version: 8.4.49
  resolution: "postcss@npm:8.4.49"
  dependencies:
    nanoid: "npm:^3.3.7"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/f1b3f17aaf36d136f59ec373459f18129908235e65dbdc3aee5eef8eba0756106f52de5ec4682e29a2eab53eb25170e7e871b3e4b52a8f1de3d344a514306be3
  languageName: node
  linkType: hard

"postcss@npm:^8.4.47":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/b75510d7b28c3ab728c8733dd01538314a18c52af426f199a3c9177e63eb08602a3938bfb66b62dc01350b9aed62087eabbf229af97a1659eb8d3513cec823b3
  languageName: node
  linkType: hard

"pretty-bytes@npm:^5.6.0":
  version: 5.6.0
  resolution: "pretty-bytes@npm:5.6.0"
  checksum: 10c0/f69f494dcc1adda98dbe0e4a36d301e8be8ff99bfde7a637b2ee2820e7cb583b0fc0f3a63b0e3752c01501185a5cf38602c7be60da41bdf84ef5b70e89c370f3
  languageName: node
  linkType: hard

"pretty-format@npm:^29.7.0":
  version: 29.7.0
  resolution: "pretty-format@npm:29.7.0"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^18.0.0"
  checksum: 10c0/edc5ff89f51916f036c62ed433506b55446ff739358de77207e63e88a28ca2894caac6e73dcb68166a606e51c8087d32d400473e6a9fdd2dbe743f46c9c0276f
  languageName: node
  linkType: hard

"proc-log@npm:^4.0.0":
  version: 4.2.0
  resolution: "proc-log@npm:4.2.0"
  checksum: 10c0/17db4757c2a5c44c1e545170e6c70a26f7de58feb985091fb1763f5081cab3d01b181fb2dd240c9f4a4255a1d9227d163d5771b7e69c9e49a561692db865efb9
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"progress@npm:^2.0.3":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: 10c0/1697e07cb1068055dbe9fe858d242368ff5d2073639e652b75a7eb1f2a1a8d4afd404d719de23c7b48481a6aa0040686310e2dac2f53d776daa2176d3f96369c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"promise@npm:^7.1.1":
  version: 7.3.1
  resolution: "promise@npm:7.3.1"
  dependencies:
    asap: "npm:~2.0.3"
  checksum: 10c0/742e5c0cc646af1f0746963b8776299701ad561ce2c70b49365d62c8db8ea3681b0a1bf0d4e2fe07910bf72f02d39e51e8e73dc8d7503c3501206ac908be107f
  languageName: node
  linkType: hard

"promise@npm:^8.3.0":
  version: 8.3.0
  resolution: "promise@npm:8.3.0"
  dependencies:
    asap: "npm:~2.0.6"
  checksum: 10c0/6fccae27a10bcce7442daf090279968086edd2e3f6cebe054b71816403e2526553edf510d13088a4d0f14d7dfa9b9dfb188cab72d6f942e186a4353b6a29c8bf
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1, prompts@npm:^2.2.1, prompts@npm:^2.3.2":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: "npm:^3.0.3"
    sisteransi: "npm:^1.0.5"
  checksum: 10c0/16f1ac2977b19fe2cf53f8411cc98db7a3c8b115c479b2ca5c82b5527cd937aa405fa04f9a5960abeb9daef53191b53b4d13e35c1f5d50e8718c76917c5f1ea4
  languageName: node
  linkType: hard

"prop-types@npm:^15.6.2, prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10c0/59ece7ca2fb9838031d73a48d4becb9a7cc1ed10e610517c7d8f19a1e02fa47f7c27d557d8a5702bec3cfeccddc853579832b43f449e54635803f277b1c78077
  languageName: node
  linkType: hard

"property-expr@npm:^2.0.5":
  version: 2.0.6
  resolution: "property-expr@npm:2.0.6"
  checksum: 10c0/69b7da15038a1146d6447c69c445306f66a33c425271235bb20507f1846dbf9577a8f9dfafe8acbfcb66f924b270157f155248308f026a68758f35fc72265b3c
  languageName: node
  linkType: hard

"psl@npm:^1.1.33":
  version: 1.15.0
  resolution: "psl@npm:1.15.0"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10c0/d8d45a99e4ca62ca12ac3c373e63d80d2368d38892daa40cfddaa1eb908be98cd549ac059783ef3a56cfd96d57ae8e2fd9ae53d1378d90d42bc661ff924e102a
  languageName: node
  linkType: hard

"punycode@npm:^2.1.1, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"pure-rand@npm:^6.0.0":
  version: 6.1.0
  resolution: "pure-rand@npm:6.1.0"
  checksum: 10c0/1abe217897bf74dcb3a0c9aba3555fe975023147b48db540aa2faf507aee91c03bf54f6aef0eb2bf59cc259a16d06b28eca37f0dc426d94f4692aeff02fb0e65
  languageName: node
  linkType: hard

"qrcode-terminal@npm:0.11.0":
  version: 0.11.0
  resolution: "qrcode-terminal@npm:0.11.0"
  bin:
    qrcode-terminal: ./bin/qrcode-terminal.js
  checksum: 10c0/7561a649d21d7672d451ada5f2a2b393f586627cea75670c97141dc2b4b4145db547e1fddf512a3552e7fb54de530d513a736cd604c840adb908ed03c32312ad
  languageName: node
  linkType: hard

"qs@npm:~6.9.1":
  version: 6.9.7
  resolution: "qs@npm:6.9.7"
  checksum: 10c0/d0274b3c2daa9e7b350fb695fc4b5f7a1e65e266d5798a07936975f0848bdca6d7ad41cded19ad4af6a6253b97e43b497e988e728eab7a286f277b6dddfbade4
  languageName: node
  linkType: hard

"query-string@npm:^7.1.3":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: "npm:^0.2.2"
    filter-obj: "npm:^1.1.0"
    split-on-first: "npm:^1.0.0"
    strict-uri-encode: "npm:^2.0.0"
  checksum: 10c0/a896c08e9e0d4f8ffd89a572d11f668c8d0f7df9c27c6f49b92ab31366d3ba0e9c331b9a620ee747893436cd1f2f821a6327e2bc9776bde2402ac6c270b801b2
  languageName: node
  linkType: hard

"querystringify@npm:^2.1.1":
  version: 2.2.0
  resolution: "querystringify@npm:2.2.0"
  checksum: 10c0/3258bc3dbdf322ff2663619afe5947c7926a6ef5fb78ad7d384602974c467fadfc8272af44f5eb8cddd0d011aae8fabf3a929a8eee4b86edcc0a21e6bd10f9aa
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"queue@npm:6.0.2":
  version: 6.0.2
  resolution: "queue@npm:6.0.2"
  dependencies:
    inherits: "npm:~2.0.3"
  checksum: 10c0/cf987476cc72e7d3aaabe23ccefaab1cd757a2b5e0c8d80b67c9575a6b5e1198807ffd4f0948a3f118b149d1111d810ee773473530b77a5c606673cac2c9c996
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"rc@npm:~1.2.7":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: "npm:^0.6.0"
    ini: "npm:~1.3.0"
    minimist: "npm:^1.2.0"
    strip-json-comments: "npm:~2.0.1"
  bin:
    rc: ./cli.js
  checksum: 10c0/24a07653150f0d9ac7168e52943cc3cb4b7a22c0e43c7dff3219977c2fdca5a2760a304a029c20811a0e79d351f57d46c9bde216193a0f73978496afc2b85b15
  languageName: node
  linkType: hard

"react-devtools-core@npm:^6.1.1":
  version: 6.1.2
  resolution: "react-devtools-core@npm:6.1.2"
  dependencies:
    shell-quote: "npm:^1.6.1"
    ws: "npm:^7"
  checksum: 10c0/a038414d69eb4d1d6303a1fc2ab0f5c2350ffa5fd8d5083ba6a83545273a8d596322c6f101c7091a2c123c47b429a91b3ea65d1291581d5bc9dd58afc62270dd
  languageName: node
  linkType: hard

"react-dom@npm:19.0.0":
  version: 19.0.0
  resolution: "react-dom@npm:19.0.0"
  dependencies:
    scheduler: "npm:^0.25.0"
  peerDependencies:
    react: ^19.0.0
  checksum: 10c0/a36ce7ab507b237ae2759c984cdaad4af4096d8199fb65b3815c16825e5cfeb7293da790a3fc2184b52bfba7ba3ff31c058c01947aff6fd1a3701632aabaa6a9
  languageName: node
  linkType: hard

"react-fast-compare@npm:^3.2.2":
  version: 3.2.2
  resolution: "react-fast-compare@npm:3.2.2"
  checksum: 10c0/0bbd2f3eb41ab2ff7380daaa55105db698d965c396df73e6874831dbafec8c4b5b08ba36ff09df01526caa3c61595247e3269558c284e37646241cba2b90a367
  languageName: node
  linkType: hard

"react-freeze@npm:^1.0.0":
  version: 1.0.4
  resolution: "react-freeze@npm:1.0.4"
  peerDependencies:
    react: ">=17.0.0"
  checksum: 10c0/8f51257c261bfefff86f618e958683536248f708019632d309ee5ebdd52f25d3c130660d06fb6f0f4fdef79f00f8ec7177233a872c2321f7d46b7e77ccc522a1
  languageName: node
  linkType: hard

"react-hook-form@npm:^7.54.2":
  version: 7.56.4
  resolution: "react-hook-form@npm:7.56.4"
  peerDependencies:
    react: ^16.8.0 || ^17 || ^18 || ^19
  checksum: 10c0/f5a7893363589fc60ae73fcf4a64897c8365d7abce63bea0b9668c78aeafd2f53e1e787cd7f4584865c3d2a80adf7a20949a350885949b7069d8995ab79a79ca
  languageName: node
  linkType: hard

"react-is@npm:^16.12.0 || ^17.0.0 || ^18.0.0, react-is@npm:^18.0.0, react-is@npm:^18.3.1":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: 10c0/f2f1e60010c683479e74c63f96b09fb41603527cd131a9959e2aee1e5a8b0caf270b365e5ca77d4a6b18aae659b60a86150bb3979073528877029b35aecd2072
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^19.0.0, react-is@npm:^19.1.0":
  version: 19.1.0
  resolution: "react-is@npm:19.1.0"
  checksum: 10c0/b6c6cadd172d5d39f66d493700d137a5545c294a62ce0f8ec793d59794c97d2bed6bad227626f16bd0e90004ed7fdc8ed662a004e6edcf5d2b7ecb6e3040ea6b
  languageName: node
  linkType: hard

"react-native-animatable@npm:1.4.0":
  version: 1.4.0
  resolution: "react-native-animatable@npm:1.4.0"
  dependencies:
    prop-types: "npm:^15.8.1"
  checksum: 10c0/658f15aa3744c3d6a22a44ab4609afc4335ff06d0a200a1fba513e470ebb4bd561ab709204b3777ee53cad526f695e2b4290e057a50fcd461aca337e407fe2b1
  languageName: node
  linkType: hard

"react-native-autocomplete-input@npm:^5.5.6":
  version: 5.5.6
  resolution: "react-native-autocomplete-input@npm:5.5.6"
  checksum: 10c0/c6f5348babe17d4067cf17587b18ba49cf5c2aede31631956c30843aa3342ca46cceac4440eb5fb3494ab46637ac8cee4b71ee49d3622f8787f813132a54a91f
  languageName: node
  linkType: hard

"react-native-css-interop@npm:0.1.22, react-native-css-interop@npm:^0.1.22":
  version: 0.1.22
  resolution: "react-native-css-interop@npm:0.1.22"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.22.15"
    "@babel/traverse": "npm:^7.23.0"
    "@babel/types": "npm:^7.23.0"
    debug: "npm:^4.3.7"
    lightningcss: "npm:^1.27.0"
    semver: "npm:^7.6.3"
  peerDependencies:
    react: ">=18"
    react-native: "*"
    react-native-reanimated: ">=3.6.2"
    tailwindcss: ~3
  peerDependenciesMeta:
    react-native-safe-area-context:
      optional: true
    react-native-svg:
      optional: true
  checksum: 10c0/d6052499b8615b58e02ee43b7d2906fd2b5ef90410c26af94dd11d6d93ed116ab0c96f7228573734030f1d41b4e8e2878a63b2223eb86c797730df207ed0c2f0
  languageName: node
  linkType: hard

"react-native-draggable-flatlist@npm:^4.0.1":
  version: 4.0.3
  resolution: "react-native-draggable-flatlist@npm:4.0.3"
  dependencies:
    "@babel/preset-typescript": "npm:^7.17.12"
  peerDependencies:
    react-native: ">=0.64.0"
    react-native-gesture-handler: ">=2.0.0"
    react-native-reanimated: ">=2.8.0"
  checksum: 10c0/1b9bd3da36929dc21bbcd7eb63e37af52c87add06776c7f20215cb6a28499f4e03619e3751fc71e192d5b074fb1dc782a939186ca7ef281928b9c940f6239455
  languageName: node
  linkType: hard

"react-native-edge-to-edge@npm:1.6.0":
  version: 1.6.0
  resolution: "react-native-edge-to-edge@npm:1.6.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/6373cc1b447eae31689a9b62e38b15621e9273626e2324700c4c3eb58c02ce489236a4b9e3e0dc1187e062defd8316195c5b1213facd718706b79b92127a05a3
  languageName: node
  linkType: hard

"react-native-gesture-handler@npm:~2.24.0":
  version: 2.24.0
  resolution: "react-native-gesture-handler@npm:2.24.0"
  dependencies:
    "@egjs/hammerjs": "npm:^2.0.17"
    hoist-non-react-statics: "npm:^3.3.0"
    invariant: "npm:^2.2.4"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/eb2c5cb53690ae5de1482370a156cbd775f6b3054540cd47310ec4712df83a280fe7b6259f372eec4c14a6d7f70ab18f1919a9fe63beaca9ceae126edbe32298
  languageName: node
  linkType: hard

"react-native-get-random-values@npm:^1.11.0":
  version: 1.11.0
  resolution: "react-native-get-random-values@npm:1.11.0"
  dependencies:
    fast-base64-decode: "npm:^1.0.0"
  peerDependencies:
    react-native: ">=0.56"
  checksum: 10c0/2ce71f1ab7f5b36d4a9dd59cc80b4aa75526f047c6680a7f1a388fa8b9a62efdacaf7b7de3be593c73e882773b2eee74916b00f7c8b158e40b46388998218586
  languageName: node
  linkType: hard

"react-native-google-places-autocomplete@npm:^2.5.7":
  version: 2.5.7
  resolution: "react-native-google-places-autocomplete@npm:2.5.7"
  dependencies:
    lodash.debounce: "npm:^4.0.8"
    prop-types: "npm:^15.7.2"
    qs: "npm:~6.9.1"
    uuid: "npm:^10.0.0"
  peerDependencies:
    react-native: ">= 0.59"
  checksum: 10c0/64d455ebcac5338a110c35f65b73445830b6ef3f454f78fac3e50f3492d509170824ec522535299b85ed315515209912d978f815d984a05be16b807a945616c2
  languageName: node
  linkType: hard

"react-native-iphone-x-helper@npm:^1.0.3":
  version: 1.3.1
  resolution: "react-native-iphone-x-helper@npm:1.3.1"
  peerDependencies:
    react-native: ">=0.42.0"
  checksum: 10c0/f2e01cb30ebdba54cbadc7bcb584ef7491c24aa2d221312f084c6e7b1031b77c2d538f7bf778ee16959245dbbe4f7784bbead02448368b7d0cbe8dd8c808693f
  languageName: node
  linkType: hard

"react-native-is-edge-to-edge@npm:1.1.7, react-native-is-edge-to-edge@npm:^1.1.6":
  version: 1.1.7
  resolution: "react-native-is-edge-to-edge@npm:1.1.7"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/b7a37437f439b1e27a4d980de01994aa71b9091dc3ed00c21172d5505fb11978cd5ed3a43f97c89d502a3a08cf26e5cea6435b8d6e93d3557a92dd43563f7021
  languageName: node
  linkType: hard

"react-native-keyboard-aware-scroll-view@npm:^0.9.5":
  version: 0.9.5
  resolution: "react-native-keyboard-aware-scroll-view@npm:0.9.5"
  dependencies:
    prop-types: "npm:^15.6.2"
    react-native-iphone-x-helper: "npm:^1.0.3"
  peerDependencies:
    react-native: ">=0.48.4"
  checksum: 10c0/932c724f86ba355f1a1db938123eef67844550dd6e2d117222eff0012a47c6a293ad79c965e16930e5eb14adb58a5c340c00e03eeabbe3d78229236125b8a6aa
  languageName: node
  linkType: hard

"react-native-modal@npm:14.0.0-rc.1":
  version: 14.0.0-rc.1
  resolution: "react-native-modal@npm:14.0.0-rc.1"
  dependencies:
    react-native-animatable: "npm:1.4.0"
  peerDependencies:
    react: "*"
    react-native: ">=0.70.0"
  checksum: 10c0/f392008da6456d73819536f2628d4fb536a0634131c957a62ac0b68f65391a0e1473fa297c9873dd5a5c8de32c380464bb48cbfaafe9cfff0abfda6db3486154
  languageName: node
  linkType: hard

"react-native-paper@npm:^5.14.5":
  version: 5.14.5
  resolution: "react-native-paper@npm:5.14.5"
  dependencies:
    "@callstack/react-theme-provider": "npm:^3.0.9"
    color: "npm:^3.1.2"
    use-latest-callback: "npm:^0.2.3"
  peerDependencies:
    react: "*"
    react-native: "*"
    react-native-safe-area-context: "*"
  checksum: 10c0/acebe9b9ccdc7d9844114defb9f9e3da0930eba0f2ec3fd6c17636ef01bb20aee59d4c5e86c7624e64ac20b0abf336912a8d19c8eafa3df83266572b03e418cd
  languageName: node
  linkType: hard

"react-native-reanimated@npm:~3.17.4":
  version: 3.17.5
  resolution: "react-native-reanimated@npm:3.17.5"
  dependencies:
    "@babel/plugin-transform-arrow-functions": "npm:^7.0.0-0"
    "@babel/plugin-transform-class-properties": "npm:^7.0.0-0"
    "@babel/plugin-transform-classes": "npm:^7.0.0-0"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.0.0-0"
    "@babel/plugin-transform-optional-chaining": "npm:^7.0.0-0"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.0.0-0"
    "@babel/plugin-transform-template-literals": "npm:^7.0.0-0"
    "@babel/plugin-transform-unicode-regex": "npm:^7.0.0-0"
    "@babel/preset-typescript": "npm:^7.16.7"
    convert-source-map: "npm:^2.0.0"
    invariant: "npm:^2.2.4"
    react-native-is-edge-to-edge: "npm:1.1.7"
  peerDependencies:
    "@babel/core": ^7.0.0-0
    react: "*"
    react-native: "*"
  checksum: 10c0/22788541546cf3e818f0ad9fc9fb1cb53fd7b398d5f49078cd6adf8064957663d97de4e60de9e7894a359d2379685a9dd5d69183c3e13b5e4e78f2d49333921a
  languageName: node
  linkType: hard

"react-native-safe-area-context@npm:5.4.0":
  version: 5.4.0
  resolution: "react-native-safe-area-context@npm:5.4.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/729fef1f768d57b905f51882374aa93b209d54576b8a0cf328e0a349c8dc9705ae8f9032e572fd7a7c9e94b588105f44760c0bb15ab9911b7977073d6754b54d
  languageName: node
  linkType: hard

"react-native-screens@npm:~4.10.0":
  version: 4.10.0
  resolution: "react-native-screens@npm:4.10.0"
  dependencies:
    react-freeze: "npm:^1.0.0"
    warn-once: "npm:^0.1.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/09d1f55431b85e556ef7b5efd776ac5e85303e47d9138f910cb8c25ff3804effc43185f84e8842bcae2219e8fee12366b3725f955f638c109387efb82e0260f3
  languageName: node
  linkType: hard

"react-native-svg-transformer@npm:^1.5.0":
  version: 1.5.1
  resolution: "react-native-svg-transformer@npm:1.5.1"
  dependencies:
    "@svgr/core": "npm:^8.1.0"
    "@svgr/plugin-jsx": "npm:^8.1.0"
    "@svgr/plugin-svgo": "npm:^8.1.0"
    path-dirname: "npm:^1.0.2"
  peerDependencies:
    react-native: ">=0.59.0"
    react-native-svg: ">=12.0.0"
  checksum: 10c0/d37dc42a242e3061fd21c7d0506414e6b84e1785139c3c9b3f01ab8131d6cc23db2042052906fa6c3e2a2a5f85a6bf642e3c69a9aaf057d0bcf64a3940a20224
  languageName: node
  linkType: hard

"react-native-svg@npm:15.11.2":
  version: 15.11.2
  resolution: "react-native-svg@npm:15.11.2"
  dependencies:
    css-select: "npm:^5.1.0"
    css-tree: "npm:^1.1.3"
    warn-once: "npm:0.1.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/040f3a298db80f4a282f9616c9550d7978beebc518de523eb417106afd7f5cb1d021424ea287cedba705f0c48aa15631056c42a79c4b46edcafa34cba71aed63
  languageName: node
  linkType: hard

"react-native-ui-datepicker@npm:^3.1.2":
  version: 3.1.2
  resolution: "react-native-ui-datepicker@npm:3.1.2"
  dependencies:
    clsx: "npm:^2.1.1"
    dayjs: "npm:^1.11.13"
    jalali-plugin-dayjs: "npm:^1.1.4"
    lodash: "npm:^4.17.21"
    tailwind-merge: "npm:^3.0.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/eea9d7f39830b4f7226e7e7348738c9df19f2f653acf64cd861243cb8cf2eb9fd9a37a49974cbc77a630bb7c61b4a2fb9e9530f14446d7e6923abc3c6dc38b80
  languageName: node
  linkType: hard

"react-native-uuid@npm:^2.0.3":
  version: 2.0.3
  resolution: "react-native-uuid@npm:2.0.3"
  checksum: 10c0/d94bccc28936c2dea081ab404f90b0c968092013af4d428b8e74bf407d6098ad5410914f77a903ad1296cce364fc85d27d8dfd70d22ec48d8cf1b7edc8803a8d
  languageName: node
  linkType: hard

"react-native-view-shot@npm:~4.0.3":
  version: 4.0.3
  resolution: "react-native-view-shot@npm:4.0.3"
  dependencies:
    html2canvas: "npm:^1.4.1"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 10c0/e172507eb7de8760a8a557a5e8632bdc248b60b89770cc941690c8fe6c788d28be1efd8259236fbc768eba3b33aaec8b7a833476b566dfef5b754618060edbec
  languageName: node
  linkType: hard

"react-native-web@npm:^0.20.0":
  version: 0.20.0
  resolution: "react-native-web@npm:0.20.0"
  dependencies:
    "@babel/runtime": "npm:^7.18.6"
    "@react-native/normalize-colors": "npm:^0.74.1"
    fbjs: "npm:^3.0.4"
    inline-style-prefixer: "npm:^7.0.1"
    memoize-one: "npm:^6.0.0"
    nullthrows: "npm:^1.1.1"
    postcss-value-parser: "npm:^4.2.0"
    styleq: "npm:^0.1.3"
  peerDependencies:
    react: ^18.0.0 || ^19.0.0
    react-dom: ^18.0.0 || ^19.0.0
  checksum: 10c0/266c16c67ccc4114864cf4facac14c3736412c937af8cf031eaaa618e801723f2c4aac5bf2d680536bbbe95602b97c13a819e775602884e900dd1362bbe2f3f5
  languageName: node
  linkType: hard

"react-native@npm:0.79.2":
  version: 0.79.2
  resolution: "react-native@npm:0.79.2"
  dependencies:
    "@jest/create-cache-key-function": "npm:^29.7.0"
    "@react-native/assets-registry": "npm:0.79.2"
    "@react-native/codegen": "npm:0.79.2"
    "@react-native/community-cli-plugin": "npm:0.79.2"
    "@react-native/gradle-plugin": "npm:0.79.2"
    "@react-native/js-polyfills": "npm:0.79.2"
    "@react-native/normalize-colors": "npm:0.79.2"
    "@react-native/virtualized-lists": "npm:0.79.2"
    abort-controller: "npm:^3.0.0"
    anser: "npm:^1.4.9"
    ansi-regex: "npm:^5.0.0"
    babel-jest: "npm:^29.7.0"
    babel-plugin-syntax-hermes-parser: "npm:0.25.1"
    base64-js: "npm:^1.5.1"
    chalk: "npm:^4.0.0"
    commander: "npm:^12.0.0"
    event-target-shim: "npm:^5.0.1"
    flow-enums-runtime: "npm:^0.0.6"
    glob: "npm:^7.1.1"
    invariant: "npm:^2.2.4"
    jest-environment-node: "npm:^29.7.0"
    memoize-one: "npm:^5.0.0"
    metro-runtime: "npm:^0.82.0"
    metro-source-map: "npm:^0.82.0"
    nullthrows: "npm:^1.1.1"
    pretty-format: "npm:^29.7.0"
    promise: "npm:^8.3.0"
    react-devtools-core: "npm:^6.1.1"
    react-refresh: "npm:^0.14.0"
    regenerator-runtime: "npm:^0.13.2"
    scheduler: "npm:0.25.0"
    semver: "npm:^7.1.3"
    stacktrace-parser: "npm:^0.1.10"
    whatwg-fetch: "npm:^3.0.0"
    ws: "npm:^6.2.3"
    yargs: "npm:^17.6.2"
  peerDependencies:
    "@types/react": ^19.0.0
    react: ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  bin:
    react-native: cli.js
  checksum: 10c0/6c9b05a74abe128a70b0bfabb286ccb2e61273af3e4a0f041f758dcf8e9b7e00722669510fb9d4d7dac6eee0078944ec01e38fd3edbb57e0ffda347c0dc07d71
  languageName: node
  linkType: hard

"react-reconciler@npm:0.31.0":
  version: 0.31.0
  resolution: "react-reconciler@npm:0.31.0"
  dependencies:
    scheduler: "npm:^0.25.0"
  peerDependencies:
    react: ^19.0.0
  checksum: 10c0/97920e1866c7206e200c3920c133c2e85f62a3c54fd9bc4b83c10c558d83d98eb378caab4fe37498e0cc1b1b2665d898627f2ae2537b29c8ab295ec8abc0c580
  languageName: node
  linkType: hard

"react-refresh@npm:^0.14.0, react-refresh@npm:^0.14.2":
  version: 0.14.2
  resolution: "react-refresh@npm:0.14.2"
  checksum: 10c0/875b72ef56b147a131e33f2abd6ec059d1989854b3ff438898e4f9310bfcc73acff709445b7ba843318a953cb9424bcc2c05af2b3d80011cee28f25aef3e2ebb
  languageName: node
  linkType: hard

"react-server-dom-webpack@npm:~19.0.0":
  version: 19.0.0
  resolution: "react-server-dom-webpack@npm:19.0.0"
  dependencies:
    acorn-loose: "npm:^8.3.0"
    neo-async: "npm:^2.6.1"
    webpack-sources: "npm:^3.2.0"
  peerDependencies:
    react: ^19.0.0
    react-dom: ^19.0.0
    webpack: ^5.59.0
  checksum: 10c0/08f15b5c21f4ec46ebf7578c1c6e8e080270274e81e969dea958d33938970d56dedbc7d598a9588bbc76ad37c1c7c0f61f8caff8ac8fb6725c94de0a9dc488fd
  languageName: node
  linkType: hard

"react-shallow-renderer@npm:^16.15.0":
  version: 16.15.0
  resolution: "react-shallow-renderer@npm:16.15.0"
  dependencies:
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.12.0 || ^17.0.0 || ^18.0.0"
  peerDependencies:
    react: ^16.0.0 || ^17.0.0 || ^18.0.0
  checksum: 10c0/c194d741792e86043a4ae272f7353c1cb9412bc649945c4220c6a101a6ea5410cceb3d65d5a4d750f11a24f7426e8eec7977e8a4e3ad5d3ee235ca2b18166fa8
  languageName: node
  linkType: hard

"react-test-renderer@npm:18.3.1":
  version: 18.3.1
  resolution: "react-test-renderer@npm:18.3.1"
  dependencies:
    react-is: "npm:^18.3.1"
    react-shallow-renderer: "npm:^16.15.0"
    scheduler: "npm:^0.23.2"
  peerDependencies:
    react: ^18.3.1
  checksum: 10c0/c633558ef9af33bc68f0c4dbb5163a004c4fb9eade7bd0a7cfc0355fb367f36bd9d96533c90b7e85a146be6c525113a15f58683d269e0177ad77e2b04d4fe51c
  languageName: node
  linkType: hard

"react-test-renderer@npm:19.0.0":
  version: 19.0.0
  resolution: "react-test-renderer@npm:19.0.0"
  dependencies:
    react-is: "npm:^19.0.0"
    scheduler: "npm:^0.25.0"
  peerDependencies:
    react: ^19.0.0
  checksum: 10c0/67c34dae4d3a60b9306d2b5cb6db436376ef20c651aaf092644298e3ffb92cd3c7b0da2017e7f1395bf2de8b42429874a5a63e8cc3c21febbab31b0309e41862
  languageName: node
  linkType: hard

"react@npm:19.0.0":
  version: 19.0.0
  resolution: "react@npm:19.0.0"
  checksum: 10c0/9cad8f103e8e3a16d15cb18a0d8115d8bd9f9e1ce3420310aea381eb42aa0a4f812cf047bb5441349257a05fba8a291515691e3cb51267279b2d2c3253f38471
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: "npm:^2.3.0"
  checksum: 10c0/90cb2750213c7dd7c80cb420654344a311fdec12944e81eb912cd82f1bc92aea21885fa6ce442e3336d9fccd663b8a7a19c46d9698e6ca55620848ab932da814
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"recast@npm:^0.23.3":
  version: 0.23.11
  resolution: "recast@npm:0.23.11"
  dependencies:
    ast-types: "npm:^0.16.1"
    esprima: "npm:~4.0.0"
    source-map: "npm:~0.6.1"
    tiny-invariant: "npm:^1.3.3"
    tslib: "npm:^2.0.1"
  checksum: 10c0/45b520a8f0868a5a24ecde495be9de3c48e69a54295d82a7331106554b75cfba75d16c909959d056e9ceed47a1be5e061e2db8b9ecbcd6ba44c2f3ef9a47bd18
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 10c0/5510785eeaf56bbfdf4e663d6753f125c08d2a372d4107bc1b756b7bf142e2ed80c2733a8b54e68fb309ba37690e66a0362699b0e21d5c1f0255dea1b00e6460
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 10c0/f73c9eba5d398c818edc71d1c6979eaa05af7a808682749dd079f8df2a6d91a9b913db216c2c9b03e0a8ba2bba8701244a93f45211afbff691c32c7b275db1b8
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.2":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 10c0/12b069dc774001fbb0014f6a28f11c09ebfe3c0d984d88c9bced77fdb6fedbacbca434d24da9ae9371bfbf23f754869307fb51a4c98a8b8b18e5ef748677ca24
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: 10c0/bbcb83a854bf96ce4005ee4e4618b71c889cda72674ce6092432f0039b47890c2d0dfeb9057d08d440999d9ea03879ebbb7f26ca005ccf94390e55c348859b98
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 10c0/44f526c4fdbf0b29286101a282189e4dbb303f4013cf3fea058668d96d113b9180d3d03d1e13f6d4cbde38b7728bf951aecd9dc199938c080093a9a6f0d7a6bd
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10c0/99d3e4e10c8c7732eb7aa843b8da2fd8b647fe144d3711b480e4647dc3bff4b1e96691ccf17f3ace24aa866a50b064236177cb25e6e4fbbb18285d99edaed83b
  languageName: node
  linkType: hard

"repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 10c0/87fa21bfdb2fbdedc44b9a5b118b7c1239bdd2c2c1e42742ef9119b7d412a5137a1d23f1a83dc6bb686f4f27429ac6f542e3d923090b44181bafa41e8ac0174d
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10c0/aaa267e0c5b022fc5fd4eef49d8285086b15f2a1c54b28240fdf03599cbd9c26049fee3eab894f2e1f6ca65e513b030a7c264201e3f005601e80c49fb2937ce2
  languageName: node
  linkType: hard

"requireg@npm:^0.2.2":
  version: 0.2.2
  resolution: "requireg@npm:0.2.2"
  dependencies:
    nested-error-stacks: "npm:~2.0.1"
    rc: "npm:~1.2.7"
    resolve: "npm:~1.7.1"
  checksum: 10c0/806cff08d8fa63f2ec9c74fa9602c86b56627a824d0a188bf777c8d82ba012a1b3c01ab6e88ffcf610713b6bc5ec8a9f9e55dc941b7606ce735e72c4d9daa059
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: 10c0/b2bfdd09db16c082c4326e573a82c0771daaf7b53b9ce8ad60ea46aa6e30aaf475fe9b164800b89f93b748d2c234d8abff945d2551ba47bf5698e04cd7713267
  languageName: node
  linkType: hard

"reselect@npm:^4.1.7":
  version: 4.1.8
  resolution: "reselect@npm:4.1.8"
  checksum: 10c0/06a305a504affcbb67dd0561ddc8306b35796199c7e15b38934c80606938a021eadcf68cfd58e7bb5e17786601c37602a3362a4665c7bf0a96c1041ceee9d0b7
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/e608a3ebd15356264653c32d7ecbc8fd702f94c6703ea4ac2fb81d9c359180cba0ae2e6b71faa446631ed6145454d5a56b227efc33a2d40638ac13f8beb20ee4
  languageName: node
  linkType: hard

"resolve-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-from@npm:3.0.0"
  checksum: 10c0/24affcf8e81f4c62f0dcabc774afe0e19c1f38e34e43daac0ddb409d79435fc3037f612b0cc129178b8c220442c3babd673e88e870d27215c99454566e770ebc
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10c0/b21cb7f1fb746de8107b9febab60095187781137fd803e6a59a76d421444b1531b641bba5857f5dc011974d8a5c635d61cec49e6bd3b7fc20e01f0fafc4efbf2
  languageName: node
  linkType: hard

"resolve-workspace-root@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve-workspace-root@npm:2.0.0"
  checksum: 10c0/658e6fbc199c51f4903867ab371f03122d9865b4fb4fd3a2069c39b429132d91535e5112f5c6c561fa0852cb8393505b7f94b58c3e2566bab610a48172f38e3f
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.0, resolve.exports@npm:^2.0.3":
  version: 2.0.3
  resolution: "resolve.exports@npm:2.0.3"
  checksum: 10c0/1ade1493f4642a6267d0a5e68faeac20b3d220f18c28b140343feb83694d8fed7a286852aef43689d16042c61e2ddb270be6578ad4a13990769e12065191200d
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7, resolve@npm:^1.14.2, resolve@npm:^1.20.0, resolve@npm:^1.22.2, resolve@npm:^1.22.8":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@npm:~1.7.1":
  version: 1.7.1
  resolution: "resolve@npm:1.7.1"
  dependencies:
    path-parse: "npm:^1.0.5"
  checksum: 10c0/6e9e29185ac57801aff013849e9717c769ef0a27eac30b6492405ba3d61db73d8967023b96578f4b2deba4ef5fb11fc4f0a4db47c0f536890ced5c014e94fbde
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.1.7#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.20.0#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.8#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A~1.7.1#optional!builtin<compat/resolve>":
  version: 1.7.1
  resolution: "resolve@patch:resolve@npm%3A1.7.1#optional!builtin<compat/resolve>::version=1.7.1&hash=3bafbf"
  dependencies:
    path-parse: "npm:^1.0.5"
  checksum: 10c0/1301dba7c12cd9dab2ab4eee8518089f25bb7480db34b746a923ded472c4c0600ebb1ba9b8028ca843f7c6017ac76524355800c52b82633e53bd601ca288b4de
  languageName: node
  linkType: hard

"restore-cursor@npm:^2.0.0":
  version: 2.0.0
  resolution: "restore-cursor@npm:2.0.0"
  dependencies:
    onetime: "npm:^2.0.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/f5b335bee06f440445e976a7031a3ef53691f9b7c4a9d42a469a0edaf8a5508158a0d561ff2b26a1f4f38783bcca2c0e5c3a44f927326f6694d5b44d7a4993e6
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"rimraf@npm:^2.6.3":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 10c0/4eef73d406c6940927479a3a9dee551e14a54faf54b31ef861250ac815172bade86cc6f7d64a4dc5e98b65e4b18a2e1c9ff3b68d296be0c748413f092bb0dd40
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"rimraf@npm:~2.6.2":
  version: 2.6.3
  resolution: "rimraf@npm:2.6.3"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 10c0/f1e646f8c567795f2916aef7aadf685b543da6b9a53e482bb04b07472c7eef2b476045ba1e29f401c301c66b630b22b815ab31fdd60c5e1ae6566ff523debf45
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 10c0/6bf86318a254c5d898ede6bd3ded15daf68ae08a5495a2739564eb265cd13bcc64a07ab466fb204f67ce472bb534eb8612dac587435515169593f4fffa11de7c
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 10c0/3847b839f060ef3476eb8623d099aa502ad658f5c40fd60c105ebce86d244389b0d76fcae30f4d0c728d7705ceb2f7e9b34bb54717b6a7dbedaf5dad2d9a4b74
  languageName: node
  linkType: hard

"scheduler@npm:0.25.0, scheduler@npm:^0.25.0":
  version: 0.25.0
  resolution: "scheduler@npm:0.25.0"
  checksum: 10c0/a4bb1da406b613ce72c1299db43759526058fdcc413999c3c3e0db8956df7633acf395cb20eb2303b6a65d658d66b6585d344460abaee8080b4aa931f10eaafe
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.2":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 10c0/26383305e249651d4c58e6705d5f8425f153211aef95f15161c151f7b8de885f24751b377e4a0b3dd42cce09aad3f87a61dab7636859c0d89b7daf1a1e2a5c78
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.1":
  version: 4.3.2
  resolution: "schema-utils@npm:4.3.2"
  dependencies:
    "@types/json-schema": "npm:^7.0.9"
    ajv: "npm:^8.9.0"
    ajv-formats: "npm:^2.1.1"
    ajv-keywords: "npm:^5.1.0"
  checksum: 10c0/981632f9bf59f35b15a9bcdac671dd183f4946fe4b055ae71a301e66a9797b95e5dd450de581eb6cca56fb6583ce8f24d67b2d9f8e1b2936612209697f6c277e
  languageName: node
  linkType: hard

"semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: 10c0/e4cf10f86f168db772ae95d86ba65b3fd6c5967c94d97c708ccb463b778c2ee53b914cd7167620950fc07faf5a564e6efe903836639e512a1aa15fbc9667fa25
  languageName: node
  linkType: hard

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.1.3, semver@npm:^7.3.5, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"semver@npm:~7.6.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 10c0/88f33e148b210c153873cb08cfe1e281d518aaa9a666d4d148add6560db5cd3c582f3a08ccb91f38d5f379ead256da9931234ed122057f40bb5766e65e58adaf
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"send@npm:^0.19.0":
  version: 0.19.1
  resolution: "send@npm:0.19.1"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ceb859859822bf55e705b96db9a909870626d1a6bfcf62a88648b9681048a7840c0ff1f4afd7babea4ccfabff7d64a7dda68a6f6c63c255cc83f40a412a1db8e
  languageName: node
  linkType: hard

"serialize-error@npm:^2.1.0":
  version: 2.1.0
  resolution: "serialize-error@npm:2.1.0"
  checksum: 10c0/919c40d293cd36b16bb3fce38a3a460e0c51a34cf0ee59815bbeec7c48ffe0a66ea2dec08aa5340ef6dfc1f22e7317f6e1ed76cdbb2ec3c494c0c4debfb344f8
  languageName: node
  linkType: hard

"serve-static@npm:^1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"server-only@npm:^0.0.1":
  version: 0.0.1
  resolution: "server-only@npm:0.0.1"
  checksum: 10c0/4704f0ef85da0be981af6d4ed8e739d39bcfd265b9c246a684060acda5642d0fdc6daffc2308e71e2682c5f508090978802eae0a77623c9b90a49f9ae68048d6
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10c0/5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: "npm:^6.0.2"
  checksum: 10c0/7bab09613a1b9f480c85a9823aebec533015579fa055ba6634aa56ba1f984380670eaf33b8217502931872aa1401c9fcadaa15f9f604d631536df475b05bcf1e
  languageName: node
  linkType: hard

"shallowequal@npm:1.1.0, shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: 10c0/b926efb51cd0f47aa9bc061add788a4a650550bbe50647962113a4579b60af2abe7b62f9b02314acc6f97151d4cf87033a2b15fc20852fae306d1a095215396c
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shell-quote@npm:^1.6.1":
  version: 1.8.2
  resolution: "shell-quote@npm:1.8.2"
  checksum: 10c0/85fdd44f2ad76e723d34eb72c753f04d847ab64e9f1f10677e3f518d0e5b0752a176fd805297b30bb8c3a1556ebe6e77d2288dbd7b7b0110c7e941e9e9c20ce1
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-plist@npm:^1.1.0":
  version: 1.4.0
  resolution: "simple-plist@npm:1.4.0"
  dependencies:
    bplist-creator: "npm:0.1.1"
    bplist-parser: "npm:0.3.2"
    plist: "npm:^3.0.5"
  checksum: 10c0/226c283492d8518d715e4133d94bdbd15c0619561bcde583b4807b36cde106c0078c615b9b4e25c0e8758a4ae4e79ed5dd76e57cd528d8b7001ecab5ad35e343
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 10c0/230ac975cca485b7f6fe2b96a711aa62a6a26ead3e6fb8ba17c5a00d61b8bed0d7adc21f5626b70d7c33c62ff4e63933017a6462942c719d1980bb0b1207ad46
  languageName: node
  linkType: hard

"slash@npm:^2.0.0":
  version: 2.0.0
  resolution: "slash@npm:2.0.0"
  checksum: 10c0/f83dbd3cb62c41bb8fcbbc6bf5473f3234b97fa1d008f571710a9d3757a28c7169e1811cad1554ccb1cc531460b3d221c9a7b37f549398d9a30707f0a5af9193
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slash@npm:^5.0.0":
  version: 5.1.0
  resolution: "slash@npm:5.1.0"
  checksum: 10c0/eb48b815caf0bdc390d0519d41b9e0556a14380f6799c72ba35caf03544d501d18befdeeef074bc9c052acf69654bc9e0d79d7f1de0866284137a40805299eb3
  languageName: node
  linkType: hard

"slugify@npm:^1.3.4, slugify@npm:^1.6.6":
  version: 1.6.6
  resolution: "slugify@npm:1.6.6"
  checksum: 10c0/e7e63f08f389a371d6228bc19d64ec84360bf0a538333446cc49dbbf3971751a6d180d2f31551188dd007a65ca771e69f574e0283290a7825a818e90b75ef44d
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/ab19a913969f58f4474fe9f6e8a026c8a2142a01f40b52b79368068343177f818cdfef0b0c6b9558f298782441d5ca8ed5932eb57822439fad791d866e62cecd
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.7.1, socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/00c3271e233ccf1fb83a3dd2060b94cc37817e0f797a93c560b9a7a86c4a0ec2961fb31263bdd24a3c28945e24868b5f063cd98744171d9e942c513454b50ae5
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.1, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/137539f8c453fa0f496ea42049ab5da4569f96781f6ac8e5bfda26937be9494f4e8891f523c5f98f0e85f71b35d74127a00c46f83f6a4f54672b58d53202565e
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.16, source-map-support@npm:~0.5.20, source-map-support@npm:~0.5.21":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:0.5.6":
  version: 0.5.6
  resolution: "source-map@npm:0.5.6"
  checksum: 10c0/beb2c5974bb58954d75e86249953d47ae16f7df1a8531abb9fcae0cd262d9fa09c2db3a134e20e99358b1adba42b6b054a32c8e16b571b3efcf6af644c329f0d
  languageName: node
  linkType: hard

"source-map@npm:^0.5.6":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 10c0/904e767bb9c494929be013017380cbba013637da1b28e5943b566031e29df04fba57edf3f093e0914be094648b577372bd8ad247fa98cfba9c600794cd16b599
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"sparse-bitfield@npm:^3.0.3":
  version: 3.0.3
  resolution: "sparse-bitfield@npm:3.0.3"
  dependencies:
    memory-pager: "npm:^1.0.2"
  checksum: 10c0/248c6ff7b5e354735e1daac4059222a29c9d291dfcf265daf675d13515eeaac454cfcccd687c8d134f86698b39abd7ad4d7434f7272dd6f8e41a00f21aae4194
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 10c0/56df8344f5a5de8521898a5c090023df1d8b8c75be6228f56c52491e0fc1617a5236f2ac3a066adb67a73231eac216ccea7b5b4a2423a543c277cb2f48d24c29
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stack-generator@npm:^2.0.5":
  version: 2.0.10
  resolution: "stack-generator@npm:2.0.10"
  dependencies:
    stackframe: "npm:^1.3.4"
  checksum: 10c0/c3f6f6c580488e65c0fee806a57f6ae4b79e6435f144be471c1f20328a8d9d8492d4f3beed31840f6dae03e2633325e2764fd3aca5c3126a0639e7c9ddfa45ce
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: "npm:^2.0.0"
  checksum: 10c0/651c9f87667e077584bbe848acaecc6049bc71979f1e9a46c7b920cad4431c388df0f51b8ad7cfd6eed3db97a2878d0fc8b3122979439ea8bac29c61c95eec8a
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: 10c0/18410f7a1e0c5d211a4effa83bdbf24adbe8faa8c34db52e1cd3e89837518c592be60b60d8b7270ac53eeeb8b807cd11b399a41667f6c9abb41059c3ccc8a989
  languageName: node
  linkType: hard

"stacktrace-gps@npm:^3.0.4":
  version: 3.1.2
  resolution: "stacktrace-gps@npm:3.1.2"
  dependencies:
    source-map: "npm:0.5.6"
    stackframe: "npm:^1.3.4"
  checksum: 10c0/0dcc1aa46e364a2b4d1eabce4777fecf337576a11ee3cfc92f07b9ec79ccb76810752431eeb9771289d250d0bb58dbe19a178b96bf7b2e9f773334d03aa96bb9
  languageName: node
  linkType: hard

"stacktrace-js@npm:^2.0.2":
  version: 2.0.2
  resolution: "stacktrace-js@npm:2.0.2"
  dependencies:
    error-stack-parser: "npm:^2.0.6"
    stack-generator: "npm:^2.0.5"
    stacktrace-gps: "npm:^3.0.4"
  checksum: 10c0/9a10c222524ca03690bcb27437b39039885223e39320367f2be36e6f750c2d198ae99189869a22c255bf60072631eb609d47e8e33661e95133686904e01121ec
  languageName: node
  linkType: hard

"stacktrace-parser@npm:^0.1.10":
  version: 0.1.11
  resolution: "stacktrace-parser@npm:0.1.11"
  dependencies:
    type-fest: "npm:^0.7.1"
  checksum: 10c0/4633d9afe8cd2f6c7fb2cebdee3cc8de7fd5f6f9736645fd08c0f66872a303061ce9cc0ccf46f4216dc94a7941b56e331012398dc0024dc25e46b5eb5d4ff018
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: 10c0/e433900956357b3efd79b1c547da4d291799ac836960c016d10a98f6a810b1b5c0dcc13b5a7aa609a58239b5190e1ea176ad9221c2157d2fd1c747393e6b2940
  languageName: node
  linkType: hard

"stream-buffers@npm:2.2.x":
  version: 2.2.0
  resolution: "stream-buffers@npm:2.2.0"
  checksum: 10c0/14a351f0a066eaa08c8c64a74f4aedd87dd7a8e59d4be224703da33dca3eb370828ee6c0ae3fff59a9c743e8098728fc95c5f052ae7741672a31e6b1430ba50a
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: 10c0/010cbc78da0e2cf833b0f5dc769e21ae74cdc5d5f5bd555f14a4a4876c8ad2c85ab8b5bdf9a722dc71a11dcd3184085e1c3c0bd50ec6bb85fffc0f28cf82597d
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: "npm:^1.0.2"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/1cd77409c3d7db7bc59406f6bcc9ef0783671dcbabb23597a1177c166906ef2ee7c8290f78cae73a8aec858768f189d2cb417797df5e15ec4eb5e16b3346340c
  languageName: node
  linkType: hard

"string-length@npm:^5.0.1":
  version: 5.0.1
  resolution: "string-length@npm:5.0.1"
  dependencies:
    char-regex: "npm:^2.0.0"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/311fa5758d397bd616be17150dfefaab4755ed292a3112237924d10ba5122f606064ad4880a293387401c1d7aa20d79f7936728bac2abed17a5e48f5b317cbc8
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.2.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: "npm:^4.1.0"
  checksum: 10c0/de4658c8a097ce3b15955bc6008f67c0790f85748bdc025b7bc8c52c7aee94bc4f9e50624516150ed173c3db72d851826cd57e7a85fe4e4bb6dbbebd5d297fdf
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 10c0/51201f50e021ef16672593d7434ca239441b7b760e905d9f33df6e4f3954ff54ec0e0a06f100d028af0982d6f25c35cd5cda2ce34eaebccd0250b8befb90d8f1
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 10c0/26abad1172d6bc48985ab9a5f96c21e440f6e7e476686de49be813b5a59b3566dccb5c525b831ec54fe348283b47f3ffb8e080bc3f965fde12e84df23f6bb7ef
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-json-comments@npm:^2.0.0, strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 10c0/b509231cbdee45064ff4f9fd73609e2bcc4e84a4d508e9dd0f31f70356473fde18abfb5838c17d56fb236f5a06b102ef115438de0600b749e818a35fbbc48c43
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"strnum@npm:^1.0.5":
  version: 1.1.2
  resolution: "strnum@npm:1.1.2"
  checksum: 10c0/a0fce2498fa3c64ce64a40dada41beb91cabe3caefa910e467dc0518ef2ebd7e4d10f8c2202a6104f1410254cae245066c0e94e2521fb4061a5cb41831952392
  languageName: node
  linkType: hard

"structured-headers@npm:^0.4.1":
  version: 0.4.1
  resolution: "structured-headers@npm:0.4.1"
  checksum: 10c0/b7d326f6fec7e7f7901d1e0542577293b5d029bf3e1fb84995e33d9aabe47d03259f64ca2d778ef5c427f6f00c78bafa051b6f233131e1556f8bb9102b11ed64
  languageName: node
  linkType: hard

"styled-components@npm:^6.1.14":
  version: 6.1.18
  resolution: "styled-components@npm:6.1.18"
  dependencies:
    "@emotion/is-prop-valid": "npm:1.2.2"
    "@emotion/unitless": "npm:0.8.1"
    "@types/stylis": "npm:4.2.5"
    css-to-react-native: "npm:3.2.0"
    csstype: "npm:3.1.3"
    postcss: "npm:8.4.49"
    shallowequal: "npm:1.1.0"
    stylis: "npm:4.3.2"
    tslib: "npm:2.6.2"
  peerDependencies:
    react: ">= 16.8.0"
    react-dom: ">= 16.8.0"
  checksum: 10c0/067778b8cf9aa24b23590d23210b0e7964c6469630e5ab821c68dac64af6e0c0270c972a836f60a8bbd9753770f0475f911fcd34eeba6bd003c233a79a391e6b
  languageName: node
  linkType: hard

"styleq@npm:^0.1.3":
  version: 0.1.3
  resolution: "styleq@npm:0.1.3"
  checksum: 10c0/975d951792e65052f1f6e41aaad46492642ce4922b3dc36d4b49b37c8509f9a776794d8f275360f00116a5e6ab1e31514bdcd5840656c4e3213da6803fa12941
  languageName: node
  linkType: hard

"stylis@npm:4.3.2":
  version: 4.3.2
  resolution: "stylis@npm:4.3.2"
  checksum: 10c0/0410e1404cbeee3388a9e17587875211ce2f014c8379af0d1e24ca55878867c9f1ccc7b0ce9a156ca53f5d6e301391a82b0645522a604674a378b3189a4a1994
  languageName: node
  linkType: hard

"sucrase@npm:3.35.0, sucrase@npm:^3.35.0":
  version: 3.35.0
  resolution: "sucrase@npm:3.35.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.2"
    commander: "npm:^4.0.0"
    glob: "npm:^10.3.10"
    lines-and-columns: "npm:^1.1.6"
    mz: "npm:^2.7.0"
    pirates: "npm:^4.0.1"
    ts-interface-checker: "npm:^0.1.9"
  bin:
    sucrase: bin/sucrase
    sucrase-node: bin/sucrase-node
  checksum: 10c0/ac85f3359d2c2ecbf5febca6a24ae9bf96c931f05fde533c22a94f59c6a74895e5d5f0e871878dfd59c2697a75ebb04e4b2224ef0bfc24ca1210735c2ec191ef
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10c0/6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.0.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: "npm:^4.0.0"
    supports-color: "npm:^7.0.0"
  checksum: 10c0/4057f0d86afb056cd799602f72d575b8fdd79001c5894bcb691176f14e870a687e7981e50bc1484980e8b688c6d5bcd4931e1609816abb5a7dc1486b7babf6a1
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"svg-parser@npm:^2.0.4":
  version: 2.0.4
  resolution: "svg-parser@npm:2.0.4"
  checksum: 10c0/02f6cb155dd7b63ebc2f44f36365bc294543bebb81b614b7628f1af3c54ab64f7e1cec20f06e252bf95bdde78441ae295a412c68ad1678f16a6907d924512b7a
  languageName: node
  linkType: hard

"svgo@npm:^3.0.2":
  version: 3.3.2
  resolution: "svgo@npm:3.3.2"
  dependencies:
    "@trysound/sax": "npm:0.2.0"
    commander: "npm:^7.2.0"
    css-select: "npm:^5.1.0"
    css-tree: "npm:^2.3.1"
    css-what: "npm:^6.1.0"
    csso: "npm:^5.0.5"
    picocolors: "npm:^1.0.0"
  bin:
    svgo: ./bin/svgo
  checksum: 10c0/a6badbd3d1d6dbb177f872787699ab34320b990d12e20798ecae915f0008796a0f3c69164f1485c9def399e0ce0a5683eb4a8045e51a5e1c364bb13a0d9f79e1
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 10c0/dfbe201ae09ac6053d163578778c53aa860a784147ecf95705de0cd23f42c851e1be7889241495e95c37cabb058edb1052f141387bef68f705afc8f9dd358509
  languageName: node
  linkType: hard

"tailwind-merge@npm:^1.14.0":
  version: 1.14.0
  resolution: "tailwind-merge@npm:1.14.0"
  checksum: 10c0/a66f5ab1a2bb2b0f5a40a031867a6bc900de98eb3339b2a51759351221527a3d600eecb6cb5a038830aa89548eba72bb63aa3856cb9f31c9a3918b42eb3df350
  languageName: node
  linkType: hard

"tailwind-merge@npm:^3.0.1":
  version: 3.3.0
  resolution: "tailwind-merge@npm:3.3.0"
  checksum: 10c0/a50cd141100486f98541dfab3705712af5860556689b7496dc6b0284374f02d12d5471f0f40035f6bb8b1c749c422060a1f3e5f8900057d8a7786b111c8472e6
  languageName: node
  linkType: hard

"tailwind-variants@npm:0.1.20":
  version: 0.1.20
  resolution: "tailwind-variants@npm:0.1.20"
  dependencies:
    tailwind-merge: "npm:^1.14.0"
  peerDependencies:
    tailwindcss: "*"
  checksum: 10c0/70fedecb635c5aa8109acb9e4d5608d37d5df82d9c9c091f6b9b2d3b7ddf5f6ca1902e3443b30a367352795d49aa5da73f47e49cc0f3c69108425df8fd95039c
  languageName: node
  linkType: hard

"tailwindcss@npm:3.4.17":
  version: 3.4.17
  resolution: "tailwindcss@npm:3.4.17"
  dependencies:
    "@alloc/quick-lru": "npm:^5.2.0"
    arg: "npm:^5.0.2"
    chokidar: "npm:^3.6.0"
    didyoumean: "npm:^1.2.2"
    dlv: "npm:^1.1.3"
    fast-glob: "npm:^3.3.2"
    glob-parent: "npm:^6.0.2"
    is-glob: "npm:^4.0.3"
    jiti: "npm:^1.21.6"
    lilconfig: "npm:^3.1.3"
    micromatch: "npm:^4.0.8"
    normalize-path: "npm:^3.0.0"
    object-hash: "npm:^3.0.0"
    picocolors: "npm:^1.1.1"
    postcss: "npm:^8.4.47"
    postcss-import: "npm:^15.1.0"
    postcss-js: "npm:^4.0.1"
    postcss-load-config: "npm:^4.0.2"
    postcss-nested: "npm:^6.2.0"
    postcss-selector-parser: "npm:^6.1.2"
    resolve: "npm:^1.22.8"
    sucrase: "npm:^3.35.0"
  bin:
    tailwind: lib/cli.js
    tailwindcss: lib/cli.js
  checksum: 10c0/cc42c6e7fdf88a5507a0d7fea37f1b4122bec158977f8c017b2ae6828741f9e6f8cb90282c6bf2bd5951fd1220a53e0a50ca58f5c1c00eb7f5d9f8b80dc4523c
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"temp-dir@npm:~2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: 10c0/b1df969e3f3f7903f3426861887ed76ba3b495f63f6d0c8e1ce22588679d9384d336df6064210fda14e640ed422e2a17d5c40d901f60e161c99482d723f4d309
  languageName: node
  linkType: hard

"temp@npm:^0.8.4":
  version: 0.8.4
  resolution: "temp@npm:0.8.4"
  dependencies:
    rimraf: "npm:~2.6.2"
  checksum: 10c0/7f071c963031bfece37e13c5da11e9bb451e4ddfc4653e23e327a2f91594102dc826ef6a693648e09a6e0eb856f507967ec759ae55635e0878091eccf411db37
  languageName: node
  linkType: hard

"terminal-link@npm:^2.1.1":
  version: 2.1.1
  resolution: "terminal-link@npm:2.1.1"
  dependencies:
    ansi-escapes: "npm:^4.2.1"
    supports-hyperlinks: "npm:^2.0.0"
  checksum: 10c0/947458a5cd5408d2ffcdb14aee50bec8fb5022ae683b896b2f08ed6db7b2e7d42780d5c8b51e930e9c322bd7c7a517f4fa7c76983d0873c83245885ac5ee13e3
  languageName: node
  linkType: hard

"terser@npm:^5.15.0":
  version: 5.39.2
  resolution: "terser@npm:5.39.2"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.14.0"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10c0/f70462feddecf458ad2441b16b2969e0024f81c6e47207717a096cfa1d60b85e0c60a129b42c80bcb258c28ae16e4e6d875db8bb9df9be9b5bc748807c2916ba
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.2"
    glob: "npm:^7.1.4"
    minimatch: "npm:^3.0.4"
  checksum: 10c0/019d33d81adff3f9f1bfcff18125fb2d3c65564f437d9be539270ee74b994986abb8260c7c2ce90e8f30162178b09dbbce33c6389273afac4f36069c48521f57
  languageName: node
  linkType: hard

"text-segmentation@npm:^1.0.3":
  version: 1.0.3
  resolution: "text-segmentation@npm:1.0.3"
  dependencies:
    utrie: "npm:^1.0.2"
  checksum: 10c0/8b9ae8524e3a332371060d0ca62f10ad49a13e954719ea689a6c3a8b8c15c8a56365ede2bb91c322fb0d44b6533785f0da603e066b7554d052999967fb72d600
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: "npm:>= 3.1.0 < 4"
  checksum: 10c0/9b896a22735e8122754fe70f1d65f7ee691c1d70b1f116fda04fea103d0f9b356e3676cb789506e3909ae0486a79a476e4914b0f92472c2e093d206aed4b7d6b
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: "npm:^1.0.0"
  checksum: 10c0/f375aeb2b05c100a456a30bc3ed07ef03a39cbdefe02e0403fb714b8c7e57eeaad1a2f5c4ecfb9ce554ce3db9c2b024eba144843cd9e344566d9fcee73b04767
  languageName: node
  linkType: hard

"throat@npm:^5.0.0":
  version: 5.0.0
  resolution: "throat@npm:5.0.0"
  checksum: 10c0/1b9c661dabf93ff9026fecd781ccfd9b507c41b9d5e581614884fffd09f3f9ebfe26d3be668ccf904fd324dd3f6efe1a3ec7f83e91b1dff9fdcc6b7d39b8bfe3
  languageName: node
  linkType: hard

"tiny-case@npm:^1.0.3":
  version: 1.0.3
  resolution: "tiny-case@npm:1.0.3"
  checksum: 10c0/c0cbed35884a322265e2cd61ff435168d1ea523f88bf3864ce14a238ae9169e732649776964283a66e4eb882e655992081d4daf8c865042e2233425866111b35
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.3.3":
  version: 1.3.3
  resolution: "tiny-invariant@npm:1.3.3"
  checksum: 10c0/65af4a07324b591a059b35269cd696aba21bef2107f29b9f5894d83cc143159a204b299553435b03874ebb5b94d019afa8b8eff241c8a4cfee95872c2e1c1c4a
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.13
  resolution: "tinyglobby@npm:0.2.13"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/ef07dfaa7b26936601d3f6d999f7928a4d1c6234c5eb36896bb88681947c0d459b7ebe797022400e555fe4b894db06e922b95d0ce60cb05fd827a0a66326b18c
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: "npm:~1.0.2"
  checksum: 10c0/69863947b8c29cabad43fe0ce65cec5bb4b481d15d4b4b21e036b060b3edbf3bc7a5541de1bacb437bb3f7c4538f669752627fdf9b4aaf034cebd172ba373408
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: 10c0/f935537799c2d1922cb5d6d3805f594388f75338fe7a4a9dac41504dd539704ca4db45b883b52e7b0aa5b2fd5ddadb1452bf95cd23a69da2f793a843f9451cc9
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"toposort@npm:^2.0.2":
  version: 2.0.2
  resolution: "toposort@npm:2.0.2"
  checksum: 10c0/ab9ca91fce4b972ccae9e2f539d755bf799a0c7eb60da07fd985fce0f14c159ed1e92305ff55697693b5bc13e300f5417db90e2593b127d421c9f6c440950222
  languageName: node
  linkType: hard

"tough-cookie@npm:^4.1.2":
  version: 4.1.4
  resolution: "tough-cookie@npm:4.1.4"
  dependencies:
    psl: "npm:^1.1.33"
    punycode: "npm:^2.1.1"
    universalify: "npm:^0.2.0"
    url-parse: "npm:^1.5.3"
  checksum: 10c0/aca7ff96054f367d53d1e813e62ceb7dd2eda25d7752058a74d64b7266fd07be75908f3753a32ccf866a2f997604b414cfb1916d6e7f69bc64d9d9939b0d6c45
  languageName: node
  linkType: hard

"tr46@npm:^3.0.0":
  version: 3.0.0
  resolution: "tr46@npm:3.0.0"
  dependencies:
    punycode: "npm:^2.1.1"
  checksum: 10c0/cdc47cad3a9d0b6cb293e39ccb1066695ae6fdd39b9e4f351b010835a1f8b4f3a6dc3a55e896b421371187f22b48d7dac1b693de4f6551bdef7b6ab6735dfe3b
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"ts-interface-checker@npm:^0.1.9":
  version: 0.1.13
  resolution: "ts-interface-checker@npm:0.1.13"
  checksum: 10c0/232509f1b84192d07b81d1e9b9677088e590ac1303436da1e92b296e9be8e31ea042e3e1fd3d29b1742ad2c959e95afe30f63117b8f1bc3a3850070a5142fea7
  languageName: node
  linkType: hard

"tsconfig@npm:7":
  version: 7.0.0
  resolution: "tsconfig@npm:7.0.0"
  dependencies:
    "@types/strip-bom": "npm:^3.0.0"
    "@types/strip-json-comments": "npm:0.0.30"
    strip-bom: "npm:^3.0.0"
    strip-json-comments: "npm:^2.0.0"
  checksum: 10c0/7a5dec94b9e42017d93041b1962c174afde00fd8f3066eea81a5e5b743065e95f3bedebff0edbe215b2517f8cdace8c9f15651a78d5eb7409cad2fc107e5eb98
  languageName: node
  linkType: hard

"tslib@npm:2.6.2":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: 10c0/e03a8a4271152c8b26604ed45535954c0a45296e32445b4b87f8a5abdb2421f40b59b4ca437c4346af0f28179780d604094eb64546bee2019d903d01c6c19bdb
  languageName: node
  linkType: hard

"tslib@npm:^2.0.1, tslib@npm:^2.0.3, tslib@npm:^2.6.2, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 10c0/8fb9a51d3f365a7de84ab7f73b653534b61b622aa6800aecdb0f1095a4a646d3f5eb295322127b6573db7982afcd40ab492d038cf825a42093a58b1e1353e0bd
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-fest@npm:^0.7.1":
  version: 0.7.1
  resolution: "type-fest@npm:0.7.1"
  checksum: 10c0/ce6b5ef806a76bf08d0daa78d65e61f24d9a0380bd1f1df36ffb61f84d14a0985c3a921923cf4b97831278cb6fa9bf1b89c751df09407e0510b14e8c081e4e0f
  languageName: node
  linkType: hard

"type-fest@npm:^2.19.0":
  version: 2.19.0
  resolution: "type-fest@npm:2.19.0"
  checksum: 10c0/a5a7ecf2e654251613218c215c7493574594951c08e52ab9881c9df6a6da0aeca7528c213c622bc374b4e0cb5c443aa3ab758da4e3c959783ce884c3194e12cb
  languageName: node
  linkType: hard

"typescript@npm:^5.6.3, typescript@npm:~5.8.3":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5f8bb01196e542e64d44db3d16ee0e4063ce4f3e3966df6005f2588e86d91c03e1fb131c2581baf0fb65ee79669eea6e161cd448178986587e9f6844446dbb48
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.6.3#optional!builtin<compat/typescript>, typescript@patch:typescript@npm%3A~5.8.3#optional!builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/39117e346ff8ebd87ae1510b3a77d5d92dae5a89bde588c747d25da5c146603a99c8ee588c7ef80faaf123d89ed46f6dbd918d534d641083177d5fac38b8a1cb
  languageName: node
  linkType: hard

"ua-parser-js@npm:^1.0.35":
  version: 1.0.40
  resolution: "ua-parser-js@npm:1.0.40"
  bin:
    ua-parser-js: script/cli.js
  checksum: 10c0/2b6ac642c74323957dae142c31f72287f2420c12dced9603d989b96c132b80232779c429b296d7de4012ef8b64e0d8fadc53c639ef06633ce13d785a78b5be6c
  languageName: node
  linkType: hard

"underscore@npm:1.7.x":
  version: 1.7.0
  resolution: "underscore@npm:1.7.0"
  checksum: 10c0/03d6d187c88031c8bf6fada822f43e956974b87dfd37232960e637dc10036968596b644ba4a65bdf09390493eff121d780e276c016addea6e2b3d5b6dd848696
  languageName: node
  linkType: hard

"underscore@npm:1.8.3":
  version: 1.8.3
  resolution: "underscore@npm:1.8.3"
  checksum: 10c0/b9f8c5756252e01c057859c4b0932dc0bea73122121bbe465f1e1fa975ca6c3b6a3c8b967b7918d374686f9f1beb96c0d5c5d52788fc1328992bff02dba3599c
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10c0/c01ed51829b10aa72fc3ce64b747f8e74ae9b60eafa19a7b46ef624403508a54c526ffab06a14a26b3120d055e1104d7abe7c9017e83ced038ea5cf52f8d5e04
  languageName: node
  linkType: hard

"undici@npm:^6.18.2":
  version: 6.21.3
  resolution: "undici@npm:6.21.3"
  checksum: 10c0/294da109853fad7a6ef5a172ad0ca3fb3f1f60cf34703d062a5ec967daf69ad8c03b52e6d536c5cba3bb65615769bf08e5b30798915cbccdddaca01045173dda
  languageName: node
  linkType: hard

"undici@npm:^6.18.2 || ^7.0.0":
  version: 7.9.0
  resolution: "undici@npm:7.9.0"
  checksum: 10c0/d024e1e4930e7bae47376824dfe82f736d59b4ddd1750ceb54fa842c955c75796df29322bf09632c4a6fb07cee9850cadc3d2d1c337bc41ef784a34449c86d04
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 10c0/f83bc492fdbe662860795ef37a85910944df7310cac91bd778f1c19ebc911e8b9cde84e703de631e5a2fcca3905e39896f8fc5fc6a44ddaf7f4aff1cda24f381
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 10c0/4d05252cecaf5c8e36d78dc5332e03b334c6242faf7cf16b3658525441386c0a03b5f603d42cbec0f09bb63b9fd25c9b3b09667aee75463cac3efadae2cd17ec
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 10c0/1d0a2deefd97974ddff5b7cb84f9884177f4489928dfcebb4b2b091d6124f2739df51fc6ea15958e1b5637ac2a24cff9bf21ea81e45335086ac52c0b4c717d6d
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 10c0/50ded3f8c963c7785e48c510a3b7c6bc4e08a579551489aa0349680a35b1ceceec122e33b2b6c1b579d0be2250f34bb163ac35f5f8695fe10bbc67fb757f0af8
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unique-string@npm:~2.0.0":
  version: 2.0.0
  resolution: "unique-string@npm:2.0.0"
  dependencies:
    crypto-random-string: "npm:^2.0.0"
  checksum: 10c0/11820db0a4ba069d174bedfa96c588fc2c96b083066fafa186851e563951d0de78181ac79c744c1ed28b51f9d82ac5b8196ff3e4560d0178046ef455d8c2244b
  languageName: node
  linkType: hard

"universalify@npm:^0.2.0":
  version: 0.2.0
  resolution: "universalify@npm:0.2.0"
  checksum: 10c0/cedbe4d4ca3967edf24c0800cfc161c5a15e240dac28e3ce575c689abc11f2c81ccc6532c8752af3b40f9120fb5e454abecd359e164f4f6aa44c29cd37e194fe
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"url-parse@npm:^1.5.3":
  version: 1.5.10
  resolution: "url-parse@npm:1.5.10"
  dependencies:
    querystringify: "npm:^2.1.1"
    requires-port: "npm:^1.0.0"
  checksum: 10c0/bd5aa9389f896974beb851c112f63b466505a04b4807cea2e5a3b7092f6fbb75316f0491ea84e44f66fed55f1b440df5195d7e3a8203f64fcefa19d182f5be87
  languageName: node
  linkType: hard

"use-latest-callback@npm:^0.2.3":
  version: 0.2.3
  resolution: "use-latest-callback@npm:0.2.3"
  peerDependencies:
    react: ">=16.8"
  checksum: 10c0/dc87503f6279ce2980f78e1019231ba20d7509e9d17adac05285babe4d6ba6f68c52f4ef7b5ad777cbc2af9fbaaa09d7adb664ca556da0aebab9f020022880be
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.5.0":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/1b8663515c0be34fa653feb724fdcce3984037c78dd4a18f68b2c8be55cc1a1084c578d5b75f158d41b5ddffc2bf5600766d1af3c19c8e329bb20af2ec6f52f4
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"utrie@npm:^1.0.2":
  version: 1.0.2
  resolution: "utrie@npm:1.0.2"
  dependencies:
    base64-arraybuffer: "npm:^1.0.2"
  checksum: 10c0/eaffe645bd81a39e4bc3abb23df5895e9961dbdd49748ef3b173529e8b06ce9dd1163e9705d5309a1c61ee41ffcb825e2043bc0fd1659845ffbdf4b1515dfdb4
  languageName: node
  linkType: hard

"uuid@npm:^10.0.0":
  version: 10.0.0
  resolution: "uuid@npm:10.0.0"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/eab18c27fe4ab9fb9709a5d5f40119b45f2ec8314f8d4cf12ce27e4c6f4ffa4a6321dc7db6c515068fa373c075b49691ba969f0010bf37f44c37ca40cd6bf7fe
  languageName: node
  linkType: hard

"uuid@npm:^7.0.3":
  version: 7.0.3
  resolution: "uuid@npm:7.0.3"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/2eee5723b0fcce8256f5bfd3112af6c453b5471db00af9c3533e3d5a6e57de83513f9a145a570890457bd7abf2c2aa05797291d950ac666e5a074895dc63168b
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/1607dd32ac7fc22f2d8f77051e6a64845c9bce5cd3dd8aa0070c074ec73e666a1f63c7b4e0f4bf2bc8b9d59dc85a15e17807446d9d2b17c8485fbc2147b27f9b
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.12"
    "@types/istanbul-lib-coverage": "npm:^2.0.1"
    convert-source-map: "npm:^2.0.0"
  checksum: 10c0/968bcf1c7c88c04df1ffb463c179558a2ec17aa49e49376120504958239d9e9dad5281aa05f2a78542b8557f2be0b0b4c325710262f3b838b40d703d5ed30c23
  languageName: node
  linkType: hard

"validate-npm-package-name@npm:^5.0.0":
  version: 5.0.1
  resolution: "validate-npm-package-name@npm:5.0.1"
  checksum: 10c0/903e738f7387404bb72f7ac34e45d7010c877abd2803dc2d614612527927a40a6d024420033132e667b1bade94544b8a1f65c9431a4eb30d0ce0d80093cd1f74
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vlq@npm:^1.0.0":
  version: 1.0.1
  resolution: "vlq@npm:1.0.1"
  checksum: 10c0/a8ec5c95d747c840198f20b4973327fa317b98397f341e7a2f352bfcf385aeb73c0eea01cc6d406c20169298375397e259efc317aec53c8ffc001ec998204aed
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^4.0.0":
  version: 4.0.0
  resolution: "w3c-xmlserializer@npm:4.0.0"
  dependencies:
    xml-name-validator: "npm:^4.0.0"
  checksum: 10c0/02cc66d6efc590bd630086cd88252444120f5feec5c4043932b0d0f74f8b060512f79dc77eb093a7ad04b4f02f39da79ce4af47ceb600f2bf9eacdc83204b1a8
  languageName: node
  linkType: hard

"walker@npm:^1.0.7, walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: "npm:1.0.12"
  checksum: 10c0/a17e037bccd3ca8a25a80cb850903facdfed0de4864bd8728f1782370715d679fa72e0a0f5da7c1c1379365159901e5935f35be531229da53bbfc0efdabdb48e
  languageName: node
  linkType: hard

"warn-once@npm:0.1.1, warn-once@npm:^0.1.0, warn-once@npm:^0.1.1":
  version: 0.1.1
  resolution: "warn-once@npm:0.1.1"
  checksum: 10c0/f531e7b2382124f51e6d8f97b8c865246db8ab6ff4e53257a2d274e0f02b97d7201eb35db481843dc155815e154ad7afb53b01c4d4db15fb5aa073562496aff7
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 10c0/5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webidl-conversions@npm:^5.0.0":
  version: 5.0.0
  resolution: "webidl-conversions@npm:5.0.0"
  checksum: 10c0/bf31df332ed11e1114bfcae7712d9ab2c37e7faa60ba32d8fdbee785937c0b012eee235c19d2b5d84f5072db84a160e8d08dd382da7f850feec26a4f46add8ff
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: 10c0/228d8cb6d270c23b0720cb2d95c579202db3aaf8f633b4e9dd94ec2000a04e7e6e43b76a94509cdb30479bd00ae253ab2371a2da9f81446cc313f89a4213a2c4
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.0":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 10c0/2ef63d77c4fad39de4a6db17323d75eb92897b32674e97d76f0a1e87c003882fc038571266ad0ef581ac734cbe20952912aaa26155f1905e96ce251adbb1eb4e
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^2.0.0":
  version: 2.0.0
  resolution: "whatwg-encoding@npm:2.0.0"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10c0/91b90a49f312dc751496fd23a7e68981e62f33afe938b97281ad766235c4872fc4e66319f925c5e9001502b3040dd25a33b02a9c693b73a4cbbfdc4ad10c3e3e
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.0.0":
  version: 3.6.20
  resolution: "whatwg-fetch@npm:3.6.20"
  checksum: 10c0/fa972dd14091321d38f36a4d062298df58c2248393ef9e8b154493c347c62e2756e25be29c16277396046d6eaa4b11bd174f34e6403fff6aaca9fb30fa1ff46d
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^3.0.0":
  version: 3.0.0
  resolution: "whatwg-mimetype@npm:3.0.0"
  checksum: 10c0/323895a1cda29a5fb0b9ca82831d2c316309fede0365047c4c323073e3239067a304a09a1f4b123b9532641ab604203f33a1403b5ca6a62ef405bcd7a204080f
  languageName: node
  linkType: hard

"whatwg-url-without-unicode@npm:8.0.0-3":
  version: 8.0.0-3
  resolution: "whatwg-url-without-unicode@npm:8.0.0-3"
  dependencies:
    buffer: "npm:^5.4.3"
    punycode: "npm:^2.1.1"
    webidl-conversions: "npm:^5.0.0"
  checksum: 10c0/c27a637ab7d01981b2e2f576fde2113b9c42247500e093d2f5ba94b515d5c86dbcf70e5cad4b21b8813185f21fa1b4846f53c79fa87995293457e28c889cc0fd
  languageName: node
  linkType: hard

"whatwg-url@npm:^11.0.0":
  version: 11.0.0
  resolution: "whatwg-url@npm:11.0.0"
  dependencies:
    tr46: "npm:^3.0.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10c0/f7ec264976d7c725e0696fcaf9ebe056e14422eacbf92fdbb4462034609cba7d0c85ffa1aab05e9309d42969bcf04632ba5ed3f3882c516d7b093053315bf4c1
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wonka@npm:^6.3.2":
  version: 6.3.5
  resolution: "wonka@npm:6.3.5"
  checksum: 10c0/044fe5ae26c0a32b0a1603cc0ed71ede8c9febe5bb3adab4fad5e088ceee600a84a08d0deb95a72189bbaf0d510282d183b6fb7b6e9837e7a1c9b209f788dd07
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"write-file-atomic@npm:^2.3.0":
  version: 2.4.3
  resolution: "write-file-atomic@npm:2.4.3"
  dependencies:
    graceful-fs: "npm:^4.1.11"
    imurmurhash: "npm:^0.1.4"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/8cb4bba0c1ab814a9b127844da0db4fb8c5e06ddbe6317b8b319377c73b283673036c8b9360120062898508b9428d81611cf7fa97584504a00bc179b2a580b92
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.2":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: "npm:^0.1.4"
    signal-exit: "npm:^3.0.7"
  checksum: 10c0/a2c282c95ef5d8e1c27b335ae897b5eca00e85590d92a3fd69a437919b7b93ff36a69ea04145da55829d2164e724bc62202cdb5f4b208b425aba0807889375c7
  languageName: node
  linkType: hard

"ws@npm:^6.2.3":
  version: 6.2.3
  resolution: "ws@npm:6.2.3"
  dependencies:
    async-limiter: "npm:~1.0.0"
  checksum: 10c0/56a35b9799993cea7ce2260197e7879f21bbbb194a967f31acbbda6f7f46ecda4365951966fb062044c95197e19fb2f053be6f65c172435455186835f494de41
  languageName: node
  linkType: hard

"ws@npm:^7, ws@npm:^7.5.10":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/bd7d5f4aaf04fae7960c23dcb6c6375d525e00f795dd20b9385902bd008c40a94d3db3ce97d878acc7573df852056ca546328b27b39f47609f80fb22a0a9b61d
  languageName: node
  linkType: hard

"ws@npm:^8.11.0, ws@npm:^8.12.1":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/4b50f67931b8c6943c893f59c524f0e4905bbd183016cfb0f2b8653aa7f28dad4e456b9d99d285bbb67cca4fedd9ce90dfdfaa82b898a11414ebd66ee99141e4
  languageName: node
  linkType: hard

"xcode@npm:^3.0.1":
  version: 3.0.1
  resolution: "xcode@npm:3.0.1"
  dependencies:
    simple-plist: "npm:^1.1.0"
    uuid: "npm:^7.0.3"
  checksum: 10c0/51bf35cee52909aeb18f868ecf9828f93b8042fadf968159320f9f11e757a52e43f6563a53b586986cfe5a34d576f3300c4c0cf1e14300084344ae206eaa53c3
  languageName: node
  linkType: hard

"xml-name-validator@npm:^4.0.0":
  version: 4.0.0
  resolution: "xml-name-validator@npm:4.0.0"
  checksum: 10c0/c1bfa219d64e56fee265b2bd31b2fcecefc063ee802da1e73bad1f21d7afd89b943c9e2c97af2942f60b1ad46f915a4c81e00039c7d398b53cf410e29d3c30bd
  languageName: node
  linkType: hard

"xml2js@npm:0.6.0":
  version: 0.6.0
  resolution: "xml2js@npm:0.6.0"
  dependencies:
    sax: "npm:>=0.6.0"
    xmlbuilder: "npm:~11.0.0"
  checksum: 10c0/db1ad659210eda4b77929aa692271308ec7e04830112161b8c707f3bcc7138947409c8461ae5c8bcb36b378d62594a8d1cb78770ff5c3dc46a68c67a0838b486
  languageName: node
  linkType: hard

"xmlbuilder@npm:^15.1.1":
  version: 15.1.1
  resolution: "xmlbuilder@npm:15.1.1"
  checksum: 10c0/665266a8916498ff8d82b3d46d3993913477a254b98149ff7cff060d9b7cc0db7cf5a3dae99aed92355254a808c0e2e3ec74ad1b04aa1061bdb8dfbea26c18b8
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 10c0/74b979f89a0a129926bc786b913459bdbcefa809afaa551c5ab83f89b1915bdaea14c11c759284bb9b931e3b53004dbc2181e21d3ca9553eeb0b2a7b4e40c35b
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 10c0/b64b535861a6f310c5d9bfa10834cf49127c71922c297da9d4d1b45eeaae40bf9b4363275876088fbe2667e5db028d2cd4f8ee72eed9bede840a67d57dab7593
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^2.2.2, yaml@npm:^2.3.4":
  version: 2.8.0
  resolution: "yaml@npm:2.8.0"
  bin:
    yaml: bin.mjs
  checksum: 10c0/f6f7310cf7264a8107e72c1376f4de37389945d2fb4656f8060eca83f01d2d703f9d1b925dd8f39852a57034fafefde6225409ddd9f22aebfda16c6141b71858
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs@npm:^17.3.1, yargs@npm:^17.6.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"yup@npm:^1.6.1":
  version: 1.6.1
  resolution: "yup@npm:1.6.1"
  dependencies:
    property-expr: "npm:^2.0.5"
    tiny-case: "npm:^1.0.3"
    toposort: "npm:^2.0.2"
    type-fest: "npm:^2.19.0"
  checksum: 10c0/84c2b53996e8001041239cf2719851719f67063ec7cd843067df73562353216f5ad4f8a222319696882d5a6058e528fade1e463c59d70cbffb41b99cd0d7571b
  languageName: node
  linkType: hard
