import React from 'react';
import { Text, View } from 'react-native';
import { DeleteButton, FieldGroupView, FieldLabel, FieldView } from './styles';

interface FieldProps {
  label: string;
  children?: React.ReactNode;
}

export function Field({ label, children }: FieldProps) {
  return (
    <FieldView>
      <FieldLabel>{label}</FieldLabel>
      {children}
    </FieldView>
  );
}

interface FieldGroupProps {
  children: React.ReactNode;
  bottomSpacing?: number;
  isDeletable?: boolean;
  onDelete?: () => void;
  containerStyle?: object;
}

export function FieldGroup({
  children,
  bottomSpacing = 16,
  isDeletable,
  onDelete,
  containerStyle,
}: FieldGroupProps) {
  const childrenArray = React.Children.toArray(children);

  return (
    <FieldGroupView style={{ marginBottom: bottomSpacing, ...containerStyle }}>
      {isDeletable && !!onDelete && (
        <DeleteButton activeOpacity={0.8} onPress={onDelete}>
          <Text style={{ color: '#fff', fontSize: 20, lineHeight: 20 }}>-</Text>
        </DeleteButton>
      )}
      {childrenArray.map((child, index) => (
        <View
          key={index}
          style={
            index !== childrenArray.length - 1
              ? {
                  borderBottomWidth: 1,
                  borderBottomColor: '#a1a1a1',
                }
              : {}
          }
        >
          {child}
        </View>
      ))}
    </FieldGroupView>
  );
}
