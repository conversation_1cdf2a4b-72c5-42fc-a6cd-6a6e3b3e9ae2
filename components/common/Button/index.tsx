import { ViewStyle } from 'react-native';
import {
  ButtonContainer,
  ButtonInverted,
  ButtonLined,
  ButtonNav,
  ButtonNavText,
  ButtonRed,
  ButtonText,
  ButtonTransparent,
} from './styles';

type buttonColor = 'green' | 'red';

import { ActivityIndicator } from 'react-native';
import { useTheme } from 'styled-components';
interface ButtonProps {
  isDisabled?: boolean;
  title: string;
  onPress?: () => void;
  isLoading?: boolean;
  isLined?: boolean;
  isInverted?: boolean;
  isTransparent?: boolean;
  isNav?: boolean;
  isActive?: boolean;
  buttonColor?: buttonColor;
  style?: ViewStyle;
  px?: number; // Padding Horizontal
  py?: number; // Padding Vertical
}

export default function Button({
  isDisabled,
  isLoading,
  isLined,
  isInverted,
  isTransparent,
  buttonColor = 'green',
  isNav,
  isActive,
  ...props
}: ButtonProps) {
  const theme = useTheme();

  if (isLoading) {
    return (
      <ButtonContainer
        disabled={isDisabled}
        buttonColor={buttonColor}
        {...props}
      >
        <ActivityIndicator size="small" color="#fff" />
      </ButtonContainer>
    );
  }

  if (isTransparent) {
    if (buttonColor === 'red') {
      return (
        <ButtonTransparent disabled={isDisabled} {...props}>
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.brand.red} />
          ) : (
            <ButtonText isRed>{props.title}</ButtonText>
          )}
        </ButtonTransparent>
      );
    }

    return (
      <ButtonTransparent disabled={isDisabled} {...props}>
        {isLoading ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <ButtonText>{props.title}</ButtonText>
        )}
      </ButtonTransparent>
    );
  }

  if (isLined) {
    if (buttonColor === 'red') {
      return (
        <ButtonLined isRed disabled={isDisabled} {...props}>
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.brand.red} />
          ) : (
            <ButtonText isLined isRed>
              {props.title}
            </ButtonText>
          )}
        </ButtonLined>
      );
    }

    return (
      <ButtonLined disabled={isDisabled} {...props}>
        <ButtonText isLined>{props.title}</ButtonText>
      </ButtonLined>
    );
  }

  if (isNav) {
    return (
      <ButtonNav isActive={isActive} disabled={isDisabled} {...props}>
        <ButtonNavText isActive={isActive}>{props.title}</ButtonNavText>
      </ButtonNav>
    );
  }

  if (isInverted) {
    return (
      <ButtonInverted disabled={isDisabled} {...props}>
        <ButtonText isInverted>{props.title}</ButtonText>
      </ButtonInverted>
    );
  }

  if (buttonColor === 'red') {
    return (
      <ButtonRed disabled={isDisabled} {...props}>
        {isLoading ? (
          <ActivityIndicator size="small" color={theme.brand.red} />
        ) : (
          <ButtonText>{props.title}</ButtonText>
        )}
      </ButtonRed>
    );
  }

  return (
    <ButtonContainer disabled={isDisabled} {...props} activeOpacity={0.8}>
      <ButtonText>{props.title}</ButtonText>
    </ButtonContainer>
  );
}
