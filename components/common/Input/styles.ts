import styled from 'styled-components/native';

export const InputContainer = styled.TextInput<{ editable?: boolean }>`
  font-family: 'Mukta<PERSON>aan<PERSON>';
  font-size: 16px;
  width: 100%;
  height: 56px;
  border-radius: 10px;
  padding: 4px 16px;
  border: 1px solid #000000;
`;

export const InputGhostContainer = styled.TextInput<{ editable?: boolean }>`
  flex: 1;
  height: 30px;
  border-radius: 10px;
`;

export const ErrorText = styled.Text`
  color: #ff0000;
  font-size: 12px;
  font-family: 'Mukta<PERSON>aani';
  margin-top: 4px;
`;

export const IconContainer = styled.View`
  position: absolute;
  right: 10px;
  top: 0;
  bottom: 0;
  justify-content: center;
  align-items: center;
`;