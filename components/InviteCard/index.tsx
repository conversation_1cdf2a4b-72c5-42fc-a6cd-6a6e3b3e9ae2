import { View, Text } from 'react-native';
import {
  InviteCardContainer,
  InviteCardHeading,
  InviteCardText,
} from './styles';
import Button from '../common/Button';

export const InviteCard = () => {
  return (
    <InviteCardContainer>
      <InviteCardHeading>Invite a Myuse!</InviteCardHeading>
      <InviteCardText>
        Invite your friends and earn exclusive rewards when they join. Start
        referring now!
      </InviteCardText>
      <View style={{ flexDirection: 'row' }}>
        <Button
          title="Share invite link"
          isInverted
          px={30}
        />
      </View>
    </InviteCardContainer>
  );
};
