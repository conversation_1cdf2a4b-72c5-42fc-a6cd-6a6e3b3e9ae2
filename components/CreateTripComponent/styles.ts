import styled, { DefaultTheme } from 'styled-components/native';

export const CreateTripHeader = styled.View`
  position: relative;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  z-index: 1;
  width: 100%;
`;

export const CreateTripTouchable = styled.TouchableOpacity`
  border-radius: 20px;
  background-color: transparent;
`;

export const CreateTripTouchableText = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;

export const CreateTripHeading = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 20px;
  color: #1c1c1c;
`;

export const CreateTripContainer = styled.ScrollView`
  flex: 1;
  background-color: #fff;
  padding: 16px;
`;

export const TripNameTextInput = styled.TextInput`
  padding: 0;
  margin: 0;
  width: 50%;
  height: 30px;
  font-family: 'MuktaVaani';
  font-size: 16px;
`;

export const DateTouchable = styled.TouchableOpacity<{ disabled?: boolean }>`
  background-color: ${({ theme }: { theme: DefaultTheme }) =>
    theme.brand.green[500]};
  padding: 0 8px;
  border-radius: 4px;
  ${({ disabled }: { disabled: boolean }) =>
    disabled &&
    `
    pointer-events: none;
  `}
`;
