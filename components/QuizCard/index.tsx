import QuizCardImage from '@/assets/svg/quiz-card-image.svg';
import { TouchableOpacity, View } from 'react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  QuizCardDescription,
  QuizCardHeading,
  QuizCardLink,
  QuizCardView,
} from './styles';
import { useEffect, useState, useRef, useCallback } from 'react';

export const QuizCard = () => {
  const router = useRouter();
  const [hasCompletedQuiz, setHasCompletedQuiz] = useState(false);
  const isNavigating = useRef(false);

  // Reset navigation flag when screen loses focus
  useFocusEffect(
    useCallback(() => {
      return () => {
        isNavigating.current = false;
      };
    }, [])
  );

  // Check if the user has completed the quiz
  useEffect(() => {
    const checkQuizCompletion = async () => {
      try {
        const hasStyle = await AsyncStorage.getItem('userStylePreference');
        setHasCompletedQuiz(!!hasStyle);
      } catch (error) {
        console.error('Error checking quiz completion:', error);
      }
    };

    checkQuizCompletion();
  }, []);

  const handlePress = () => {
    if (isNavigating.current) return;
    isNavigating.current = true;
    router.push('/(style-quiz)');
  };

  return (
    <QuizCardView>
      <TouchableOpacity onPress={handlePress} style={{ flexDirection: 'row', alignItems: 'center' }} activeOpacity={1}  >
        <View style={{ flex: 1, marginRight: 8 }}>
          <QuizCardHeading>Style Quiz</QuizCardHeading>
          <QuizCardDescription>
            Are you ready to discover the vibe that truly represents YOU?{' '}
          </QuizCardDescription>
          <QuizCardLink>{hasCompletedQuiz ? 'Continue' : 'Let\'s get started'} &gt;</QuizCardLink>
        </View>
        <QuizCardImage />
      </TouchableOpacity>
    </QuizCardView>
  );
};
