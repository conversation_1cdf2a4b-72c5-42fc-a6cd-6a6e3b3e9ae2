import ClosetIconActive from '@/assets/svg/nav-icons/closet-active.svg';
import ClosetIcon from '@/assets/svg/nav-icons/closet.svg';
import HomeIconActive from '@/assets/svg/nav-icons/home-active.svg';
import HomeIcon from '@/assets/svg/nav-icons/home.svg';
import MusingIconActive from '@/assets/svg/nav-icons/musing-active.svg';
import MusingIcon from '@/assets/svg/nav-icons/musing.svg';
import ProfileIconActive from '@/assets/svg/nav-icons/profile-active.svg';
import ProfileIcon from '@/assets/svg/nav-icons/profile.svg';
import { PlusIcon } from 'lucide-react-native';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  CustomTabBar,
  CustomTabBarItem,
  NavText,
  TabBarItemContainer,
} from './styles';

export default function CustomTabs({
  state,
  descriptors,
  navigation,
  insets,
  onAddPress,
}: any) {
  const IconType = (route: string, isFocused: boolean) => {
    switch (route) {
      case 'home':
        return isFocused ? HomeIconActive : HomeIcon;
      case 'musings':
        return isFocused ? MusingIconActive : MusingIcon;
      case 'closet':
        return isFocused ? ClosetIconActive : ClosetIcon;
      case 'profile':
        return isFocused ? ProfileIconActive : ProfileIcon;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView edges={['bottom']}>
      <CustomTabBar>
        {state.routes.map((route: any, index: number) => {
          if (route.name === 'add') {
            return (
              <CustomTabBarItem
                key={index}
                onPress={() => {
                  onAddPress && onAddPress();
                }}
              >
                <PlusIcon size={24} color="#fff" />
              </CustomTabBarItem>
            );
          }

          const { options } = descriptors[route.key];
          const isFocused = state.index === index;
          const Icon = IconType(route.name, isFocused);

          if (!Icon) return null;

          return (
            <TabBarItemContainer
              key={index}
              onPress={() => {
                navigation.navigate(route.name);
              }}
            >
              <Icon />
              <NavText isFocused={isFocused}>{options.title}</NavText>
            </TabBarItemContainer>
          );
        })}
      </CustomTabBar>
    </SafeAreaView>
  );
}
