import Lock from '@/assets/svg/lock.svg';
import RewardBadge from '@/assets/svg/reward-badge.svg';
import { Text } from 'react-native';
import { LockIcon, RewardCardText, RewardCardView, RewardImageContainer, RewardUnlockText } from './styles';

type RewardCardProps = {
  image: React.ReactNode;
  name: string;
  isLocked?: boolean;
};

export const RewardCard = ({ image, name, isLocked }: RewardCardProps) => {
  return (
    <RewardCardView>
      {isLocked && (
        <LockIcon>
          <Lock />
        </LockIcon>
      )}
      <RewardImageContainer>{image}</RewardImageContainer>
      <RewardCardText style={{ textAlign: 'center' }}>{name}</RewardCardText>
      {isLocked ? (
        <RewardUnlockText>Unlock to earn badge and 10 points!</RewardUnlockText>
      ) : (
        <RewardBadge />
      )}
    </RewardCardView>
  );
};
