import { getUpcomingTrips } from '@/methods/trips';
import { useFocusEffect, useRouter } from 'expo-router';
import { ChevronRight } from 'lucide-react-native';
import { useCallback } from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import { useTheme } from 'styled-components';
import TripCard from '../TripCard';
import {
  SeeAllText,
  SeeAllTouchable,
  UpcomingTripsContainer,
  UpcomingTripsHeader,
  UpcomingTripsTitle,
} from './styles';

export default function UpcomingTrips() {
  const theme = useTheme();
  const router = useRouter();
  const { data: trips, refetch, isLoading } = getUpcomingTrips();

  const tripsData = trips?.data?.events;

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  return (
    <UpcomingTripsContainer>
      <UpcomingTripsHeader>
        <UpcomingTripsTitle>Upcoming</UpcomingTripsTitle>
        <SeeAllTouchable
          onPress={() => router.push('/(tabs)/all-trips')}
          activeOpacity={1}
        >
          <SeeAllText>See All</SeeAllText>
          <ChevronRight size={14} color={theme.brand.green[500]} style={{ marginLeft: 6 }} />
        </SeeAllTouchable>
      </UpcomingTripsHeader>
      <View style={{ gap: 16, marginTop: 16 }}>
        {isLoading ? (
          <ActivityIndicator size="large" color={theme.brand.green[500]} />
        ) : tripsData?.length === 0 ? (
          <View style={{ alignItems: 'center', padding: 20 }}>
            <Text style={{ fontFamily: 'MuktaVaani', color: theme.brand.green[500] }}>No upcoming trips</Text>
          </View>
        ) : (
          tripsData?.map((trip: any, index: number) => (
            <TripCard
              index={index}
              key={trip._id}
              id={trip._id}
              title={trip.name}
              destinations={trip.destinations}
              packingListItems={trip.packingList}
              isDeletable={true}
              onDelete={() => {
                refetch();
              }}
            />
          ))
        )}
      </View>
    </UpcomingTripsContainer>
  );
}
