import RightChevron from '@/assets/svg/right-chevron.svg';
import { router, useFocusEffect } from 'expo-router';
import { useCallback, useRef } from 'react';
import { View } from 'react-native';
import { RewardsGrid } from '../RewardsGrid';
import {
  PointsText,
  RewardsHeader,
  RewardsHeading,
  RewardsSeeAll,
  RewardsSeeAllText,
} from './styles';

export const RewardsSection = () => {
  const isNavigating = useRef(false);

  useFocusEffect(useCallback(() => {
    isNavigating.current = false;
  }, []));

  const handlePress = useCallback(() => {
    if (isNavigating.current) return;
    isNavigating.current = true;
    router.push('/rewards');
  }, []);

  return (
    <View>
      <RewardsHeader>
        <RewardsHeading>Rewards</RewardsHeading>
        <RewardsSeeAll onPress={handlePress}>
          <RewardsSeeAllText>See all</RewardsSeeAllText>
          <RightChevron />
        </RewardsSeeAll>
      </RewardsHeader>
      <PointsText>Points Earned: 10</PointsText>
      <RewardsGrid itemsToShow={2} />
    </View>
  );
};
