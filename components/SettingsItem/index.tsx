import RightChevronBlack from '@/assets/svg/right-chevron-black.svg';
import { updateUserPreferences } from '@/methods/users';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Text, View } from 'react-native';
import { SettingsDropdown } from '../SettingsDropdown';
import {
  SettingsItemChildrenContainer,
  SettingsItemContainer,
  SettingsTouchable,
  SettingsTouchableText,
} from './styles';
import { useQueryClient } from '@tanstack/react-query';
import { useFocusEffect } from 'expo-router';

export type SettingsOption = {
  label: string;
  value: string | boolean;
};

export type SettingsItemProps = {
  title: string;
  itemKey?: string;
  onPress?: () => void;
  options?: SettingsOption[];
  selected?: SettingsOption;
  children?: React.ReactNode;
  keywords?: string[];
};

export const SettingsItem = ({
  title,
  itemKey,
  options,
  onPress,
  selected,
  children,
  keywords,
}: SettingsItemProps) => {
  const [selectedValue, setSelectedValue] = useState<SettingsOption>({
    label: '',
    value: '',
  });

  const { mutate: updatePreferences } = updateUserPreferences();
  const queryClient = useQueryClient();
  const isNavigating = useRef(false);

  useEffect(() => {
    selected && setSelectedValue(selected);
  }, [selected]);

  useFocusEffect(useCallback(() => {
    isNavigating.current = false;
  }, []));

  const handleOnOptionSelect = useCallback(
    (item: SettingsOption) => {
      if (itemKey) {
        updatePreferences(
          { [itemKey]: item.value },
          {
            onSuccess: () =>
              queryClient.invalidateQueries({
                queryKey: ['profile'],
              }),
          },
        );
        setSelectedValue(item);
      }
      onPress && onPress();
    },
    [itemKey, onPress],
  );

  return (
    <SettingsItemContainer>
      {itemKey && options && options.length > 0 ? (
        <SettingsDropdown
          title={title}
          options={options}
          selectedItem={selectedValue}
          onSelect={handleOnOptionSelect}
        />
      ) : (
        <SettingsTouchable onPress={() => {
          if (isNavigating.current) return;
          isNavigating.current = true;
          onPress && onPress();
        }} activeOpacity={0.8}>
          <SettingsTouchableText>{title}</SettingsTouchableText>
          <RightChevronBlack />
        </SettingsTouchable>
      )}
      {children ? (
        <SettingsItemChildrenContainer>
          {children}
        </SettingsItemChildrenContainer>
      ) : keywords ? (
        <SettingsItemChildrenContainer>
          <View>
            <Text style={{ fontSize: 16, fontWeight: 600 }} numberOfLines={1}>
              {keywords.join(', ')}
            </Text>
          </View>
        </SettingsItemChildrenContainer>
      ) : null}
    </SettingsItemContainer>
  );
};
