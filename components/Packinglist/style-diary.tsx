import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
  Image,
  Dimensions,
  FlatList,
} from 'react-native';
import moment from 'moment';
import { getStylesDiary } from '@/methods/trips'; 
import { StyledDiaryItem, StyledDiaryText } from './styles';
import { PenIcon } from 'lucide-react-native';

import StyledDiaryModal from './style-diary-modal';
import { A } from '@expo/html-elements';

const { width } = Dimensions.get('window');
const CANVAS_WIDTH = width - 40; // Leaving some margin
const CANVAS_HEIGHT = CANVAS_WIDTH * 1.5; // Aspect ratio of 2:3


export const StyledDiary = ({  eventId }: { eventId: string }) => {
  const [activeDay, setActiveDay] = useState<string | null>(moment().format('MM-DD-YYYY'));
  const { data: stylesDiary, refetch } = getStylesDiary(eventId);
  const [isModalVisible, setIsModalVisible] = useState(false);


  const styleDiaryList = stylesDiary?.data?.styleDiaries;
  
  const activeStyleDiary = styleDiaryList?.find((item: any) => item.date === activeDay);


  useEffect(() => {
    if (styleDiaryList?.length > 0) {
      setActiveDay(styleDiaryList[0].date);
    }
  }, [styleDiaryList]);

  const handleModalClose = () => {
    setIsModalVisible(false);
    refetch();
  };

  const DataItems = ({item}: {item: {date: string}}) => {
    const isActive = activeDay === item.date;

    return (
      <TouchableOpacity onPress={() => setActiveDay(item.date)}>
        <StyledDiaryItem style={{ borderBottomWidth: isActive ? 1 : 0, borderBottomColor: isActive ? '#0E7E61' : 'transparent' }}> 
          <StyledDiaryText style={{ color: isActive ? '#0E7E61' : '#000000' }}>
            {moment(item.date, 'MM-DD-YYYY').format('ddd DD MMM YYYY')}
          </StyledDiaryText>
        </StyledDiaryItem>
      </TouchableOpacity> 
    );
  }
  return (
    <View style={{ marginTop: 40}}>
      <FlatList
        contentContainerStyle={{gap: 10,  paddingBottom: 10, width: '100%'}}
        horizontal
        data={styleDiaryList || []}
        renderItem={({ item }) => <DataItems item={item} />}
      />
      <View>
        <View style={{ width: '100%', borderRadius: 10, alignItems: 'flex-end', justifyContent: 'center', 
          position: 'absolute',
          top: 30,
          right: 20,
          zIndex: 1000,
        }}>
          <TouchableOpacity onPress={() => setIsModalVisible(true)}>
            <PenIcon />
          </TouchableOpacity>
        </View>
        <View style={{  width: '100%', borderRadius: 10, alignItems: 'flex-end', justifyContent: 'center',}}>
          <Image source={{ uri: activeStyleDiary?.imageURL }} style={{ width: '100%', height: 500, borderRadius: 10 }} resizeMode='contain' />
        </View>
      </View>
      <StyledDiaryModal
      activeStyleDiary={activeStyleDiary}
      isVisible={isModalVisible} onClose={handleModalClose} eventId={eventId} />
    </View>
  );
};