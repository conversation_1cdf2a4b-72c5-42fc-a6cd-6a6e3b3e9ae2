import Cocktails from '@/assets/svg/cocktails.svg';
import { RewardCard } from '@/components/RewardCard';
import { getRewards } from '@/methods/rewards';
import { FC, useEffect, useState } from 'react';
import { GridContainer } from '../GridContainer';

type RewardsGridProps = {
  itemsToShow?: number;
};

export const RewardsGrid: FC<RewardsGridProps> = ({
  itemsToShow,
}: RewardsGridProps) => {
  const [rewards, setRewards] = useState<any[]>([]);
  const { data } = getRewards();

  useEffect(() => {
    if (data && data.success) {
      setRewards(data.data.rewards);
    }
  }, [data]);

  return (
    <GridContainer itemsToShow={itemsToShow}>
      <RewardCard name="First Step in Style" image={<Cocktails />} />
      <RewardCard name="Sync your Calendar" image={<Cocktails />} isLocked />
      <RewardCard name="First Step in Style" image={<Cocktails />} />
      <RewardCard name="Sync your Calendar" image={<Cocktails />} isLocked />
      {/* {rewards.length > 0 &&
        rewards.map((reward) => (
          <RewardCard
            key={reward._id}
            name={reward.name}
            // points={reward.points}
            // description={reward.description}
            image={<Cocktails />}
            // width={childWidth}
          />
        ))} */}
    </GridContainer>
  );
};
