import { useCallback, useState } from 'react';
import {
  Modal,
  Platform,
  TouchableOpacity,
  GestureResponderEvent
} from 'react-native';
import DateTimePicker, { DateType, useDefaultStyles } from 'react-native-ui-datepicker';
import {
  Date<PERSON>icker<PERSON><PERSON>,
  DatePickerHeader,
  DatePickerHeaderButton,
  DatePickerModalContent,
  DatePickerModalOverlay,
  DatePickerTitle,
} from './styles';
import { useTheme } from 'styled-components/native';

interface DatePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onDateChange: (startDate?: Date | undefined, endDate?: Date | undefined) => void;
  value?: Date;
  title?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  mode?: 'single' | 'range' | 'multiple';
  startDate?: Date;
  endDate?: Date;
}

export default function DatePickerModal({
  visible,
  onClose,
  onDateChange,
  value,
  title,
  minimumDate,
  maximumDate,
  mode = 'single',
  startDate,
  endDate,
}: DatePickerModalProps) {
  const theme = useTheme();
  const defaultStyles = useDefaultStyles();
  const [selectedDates, setSelectedDates] = useState<DateType[]>([startDate, endDate].filter(Boolean) as DateType[]);
  const [selectedDate, setSelectedDate] = useState<DateType>(value || new Date());
  const [tempDate, setTempDate] = useState<Date | undefined>(startDate);
  const [tempEndDate, setTempEndDate] = useState<Date | undefined>(endDate);

  const handleDone = useCallback(() => {
    if (mode === 'multiple') {
      // For multiple mode, we'll use the first selected date
      const dateToUse = selectedDates[0]?.toString() || tempDate?.toString() || new Date().toString();
      onDateChange(new Date(dateToUse));
    } else if (mode === 'range') {
      const startDateToUse = tempDate?.toString() || new Date().toString();
      const endDateToUse = tempEndDate?.toString();
      onDateChange(new Date(startDateToUse), endDateToUse ? new Date(endDateToUse) : undefined);
    } else {
      const dateToUse = tempDate?.toString() || new Date().toString();
      onDateChange(new Date(dateToUse));
    }
    onClose();
  }, [onDateChange, onClose, tempDate, mode, selectedDates, tempEndDate]);

  const renderDateTimePicker = () => {
    const fontStyles = {
      fontFamily: 'MuktaVaani',
      fontSize: 16,
    };
    const commonProps = {
      minDate: minimumDate,
      maxDate: maximumDate,
      styles: {
        ...defaultStyles,
        selected: { backgroundColor: theme.brand.green[500] },
        selected_month: { backgroundColor: theme.brand.green[500] },
        selected_year: { backgroundColor: theme.brand.green[500] },
        selected_time: { backgroundColor: theme.brand.green[500] },
        year_label: { ...fontStyles },
        month_label: { ...fontStyles },
        day_label: { ...fontStyles },
        time_label: { ...fontStyles },
        outside_label: { ...fontStyles },
        today_label: { ...fontStyles },
        month_selector_label: { ...fontStyles, fontSize: 20 },
        year_selector_label: { ...fontStyles, fontSize: 20 },
        time_selector_label: { ...fontStyles, fontSize: 20 },
      },
      calendar: "gregory" as const,
    };

    switch (mode) {
      case 'single':
        return (
          <DateTimePicker
            {...commonProps}
            date={selectedDate}
            onChange={({ date }: { date: DateType }) => {
              if (date) {
                setSelectedDate(date);
                setTempDate(new Date(date.toString()));
              }
            }}
            mode="single"
          />
        );
      case 'range':
        return (
          <DateTimePicker
            {...commonProps}
            startDate={tempDate}
            endDate={tempEndDate}
            onChange={({ startDate, endDate }: { startDate: DateType; endDate: DateType }) => {
              // If we have both dates, update them
              if (startDate && endDate) {
                setTempDate(new Date(startDate.toString()));
                setTempEndDate(new Date(endDate.toString()));
              }
              // If we only have start date, update it
              else if (startDate) {
                setTempDate(new Date(startDate.toString()));
              }
              // If we only have end date, update it
              else if (endDate) {
                setTempEndDate(new Date(endDate.toString()));
              }
            }}
            mode="range"
          />
        );
      case 'multiple':
        return (
          <DateTimePicker
            {...commonProps}
            dates={selectedDates}
            onChange={({ dates }: { dates: DateType[] }) => {
              if (dates && dates.length > 0 && dates[0]) {
                setSelectedDates(dates);
                setTempDate(new Date(dates[0].toString()));
              }
            }}
            mode="multiple"
          />
        );
      default:
        return null;
    }
  };

  return (
    <Modal visible={visible} transparent animationType={Platform.OS === 'ios' ? 'fade' : 'none'}>
      <DatePickerModalOverlay onPress={handleDone} activeOpacity={1}>
        <DatePickerModalContent activeOpacity={1} onPress={(e: GestureResponderEvent) => {
          e.stopPropagation();
          e.preventDefault();
        }}>
          <DatePickerHeader>
            <TouchableOpacity onPress={onClose}>
              <DatePickerHeaderButton>Cancel</DatePickerHeaderButton>
            </TouchableOpacity>
            <DatePickerTitle>{title}</DatePickerTitle>
            <TouchableOpacity onPress={handleDone}>
              <DatePickerHeaderButton>
                Done
              </DatePickerHeaderButton>
            </TouchableOpacity>
          </DatePickerHeader>
          <DatePickerBody>
            {renderDateTimePicker()}
          </DatePickerBody>
        </DatePickerModalContent>
      </DatePickerModalOverlay>
    </Modal>
  );
}
