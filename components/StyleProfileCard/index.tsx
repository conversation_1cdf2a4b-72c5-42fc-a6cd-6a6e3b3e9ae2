import { TouchableOpacity, View } from "react-native";
import { StyleProfileCardDescription, StyleProfileCardHeading, StyleProfileCardLink, StyleProfileCardView } from "./styles";
import { useCallback } from "react";

type StyleProfileCardProps = {
  hasStyleProfile: boolean;
};

export const StyleProfileCard = ({ hasStyleProfile }: StyleProfileCardProps) => {
  const handlePress = useCallback(() => {
  }, []);

  return (
    <StyleProfileCardView>
      <TouchableOpacity onPress={handlePress} style={{ flexDirection: 'row', alignItems: 'center' }} activeOpacity={1}  >
        <View style={{ flex: 1, marginRight: 8 }}>
          <StyleProfileCardHeading>Set your style profile</StyleProfileCardHeading>
          <StyleProfileCardDescription>
            Enhance your design choices by setting your preferences!
          </StyleProfileCardDescription>
          <StyleProfileCardLink>Visit style profile &gt;</StyleProfileCardLink>
        </View>
      </TouchableOpacity>
    </StyleProfileCardView>
  );
};