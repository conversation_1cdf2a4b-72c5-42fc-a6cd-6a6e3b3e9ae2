declare module '@meteorrn/core' {
  export function withTracker(component: any): any;
  export default class Meteor {
    static connect(url: string, options?: any): void;
    static status(): { connected: boolean };
    static userId(): string | null;
    static call(method: string, ...args: any[]): Promise<any>;
    static _loginWithToken(token: string): void;
    static logout(callback?: () => void): void;
    static loginWithPassword(email: string, password: string, callback?: (err: any, res: any) => void): void;
  }
}

// Add or update the type definition for the signUp response
interface SignUpResponse {
  success: boolean;
  data: {
    token: string; // Ensure the token is included within a data object
  };
  // other properties...
} 