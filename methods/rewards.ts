import Meteor from '@meteorrn/core';
import { useMutation, useQuery } from '@tanstack/react-query';

type Reward = {
  _id: string;
  createdAt: Date;
  description: string;
  name: string;
  points: number;
};

type RewardsResponse = {
  success: boolean;
  data: {
    rewards: Reward[];
  };
};

type RewardsCompleteResponse = {
  success: boolean;
  data: any;
};

export const getRewards = () =>
  useQuery({
    queryKey: ['rewards'],
    queryFn: () => {
      return new Promise<RewardsResponse>((resolve, reject) => {
        Meteor.call(
          'rewards-fetchAll',
          {},
          (err: any, res: RewardsResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }

            console.log('rewardsfetchAll res', res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const rewardsComplete = () =>
  useMutation({
    mutationFn: (reward: any) => {
      return new Promise<RewardsCompleteResponse>((resolve, reject) => {
        Meteor.call(
          'rewards-complete',
          { ...reward },
          (err: any, res: RewardsCompleteResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            resolve(res);
            return;
          },
        );
      });
    },
  });
