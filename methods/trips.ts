import { TripDetails } from '@/types/trip';
import Meteor from '@meteorrn/core';
import { useMutation, useQuery } from '@tanstack/react-query';

type TripResponse = {
  success: boolean;
  data: any;
};


export const createNewTrip = () =>
  useMutation({
    mutationFn: (trip: TripDetails) => {
      return new Promise<TripResponse>((resolve, reject) => {
        Meteor.call(
          'events-add',
          { ...trip },
          (err: any, res: TripResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const deleteTrip = () =>
  useMutation({
    mutationFn: (tripId: string) => {
      return new Promise<{ tripId: string }>((resolve, reject) => {
        Meteor.call(
          'events-delete',
          { eventId: tripId },
          (err: any, res: { tripId: string }) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const getTrips = () => {
  return useQuery({
    queryKey: ['trips'],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call('events-fetchAll', {}, (err: any, res: TripResponse) => {
          if (err) {
            reject(err);
          }
          console.log(res, 'res');
          resolve(res);
        });
      }),
  });
};

export const getTripById = (id: string) => {
  return useQuery({
    queryKey: ['trip', id],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call(
          'events-fetchById',
          { eventId: id },
          (err: any, res: TripResponse) => {
            if (err) {
              reject(err);
            }
            resolve(res);
          },
        );
      }),
  });
};

export const updatePackingList = () => {
  return useMutation({
    mutationFn: ({
      eventId,
      packingList,
    }: {
      eventId: string;
      packingList: any;
    }) =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call(
          'events-updatePackingList',
          { eventId, packingList },
          (err: any, res: TripResponse) => {
            if (err) {
              console.log(err, 'err');
              reject(err);
            }
            console.log('events-updatePackingList', res);
            resolve(res);
          },
        );
      }),
  });
};

export const getPackingList = (eventId: string) => {
  return useQuery({
    queryKey: ['packingList', eventId],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        if (!eventId) {
          resolve({ data: { packingList: [] } } as TripResponse); // Return empty packing list
          return;
        }

        Meteor.call(
          'events-fetchPackingListByEventId',
          { eventId },
          (err: any, res: TripResponse) => {
            if (err) {
              reject(err);
              return;
            }

            // Ensure we have a valid response with proper structure
            if (!res) {
              resolve({ data: { packingList: [] } } as TripResponse);
              return;
            }

            if (!res.data) {
              res = { ...res, data: { packingList: [] } } as TripResponse;
            }

            if (!res.data.packingList) {
              res.data = { ...res.data, packingList: [] };
            }

            if (!Array.isArray(res.data.packingList)) {
              res.data.packingList = [];
            }

            resolve(res);
          },
        );
      }),
    // Add staleTime to reduce unnecessary refetches
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const getUpcomingTrips = () => {
  return useQuery({
    queryKey: ['upcomingTrips'],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call(
          'events-fetchUpComingEvents',
          {},
          (err: any, res: TripResponse) => {
            if (err) {
              reject(err);
            }
            console.log(res, 'res');
            resolve(res);
          },
        );
      }),
  });
};

export const getPastTrips = () => {
  return useQuery({
    queryKey: ['pastTrips'],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call(
          'events-fetchPastEvents',
          {},
          (err: any, res: TripResponse) => {
            if (err) {
              reject(err);
            }
            console.log(res, 'res');
            resolve(res);
          },
        );
      }),
  });
};

export const getTemplate = () => {
  return useQuery({
    queryKey: ['template'],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call('packingListTemplates-fetchAll', {}, (err: any, res: TripResponse) => {
          if (err) {
            console.log(err, 'errsss');
            reject(err);
          }

          console.log(res, 'ressss');

          resolve(res.data);
        });
      }),
  });
};

//get styles diary
export const getStylesDiary = (eventId: string) => {
  return useQuery({
    queryKey: ['stylesDiary', eventId],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call('styleDiaries.fetchByEventId', {
          eventId: eventId,
        }, (err: any, res: TripResponse) => {
          if (err) {
            reject(err);
          }
          resolve(res);
        });
      }),
  });
};
