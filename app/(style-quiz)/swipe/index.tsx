import React, { useState, useEffect, useRef } from "react";
import {
    View,
    Image,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    Animated,
    PanResponder,
    ImageSourcePropType,
    Text,
} from "react-native";
import { Stack, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import LikeIcon from "@/assets/svg/like.svg";
import DislikeIcon from "@/assets/svg/dislike.svg";
import { useMutation } from "@tanstack/react-query";
import styled from "styled-components/native";
import { styleQuizService } from "@/lib/services/style-quiz";

interface Theme {
    colors: {
        background: string;
        gray: {
            [key: number]: string;
        };
    };
    fonts: {
        mukta: string;
    };
}

interface QuizQuestion {
    questionId: string;
    imageUrl: ImageSourcePropType;
}

// Mock data for testing - using actual images from assets
const mockQuizQuestions: QuizQuestion[] = [
    {
        questionId: "1",
        imageUrl: require("@/assets/images/style-quiz/swipe-img1.png"),
    },
    {
        questionId: "2",
        imageUrl: require("@/assets/images/style-quiz/swipe-img2.png"),
    },
    {
        questionId: "3",
        imageUrl: require("@/assets/images/style-quiz/swipe-img3.png"),
    },
    {
        questionId: "4",
        imageUrl: require("@/assets/images/style-quiz/swipe-img4.png"),
    },
    {
        questionId: "5",
        imageUrl: require("@/assets/images/style-quiz/swipe-img5.png"),
    },
    {
        questionId: "6",
        imageUrl: require("@/assets/images/style-quiz/swipe-img6.png"),
    },
    {
        questionId: "7",
        imageUrl: require("@/assets/images/style-quiz/swipe-img7.png"),
    },
    {
        questionId: "8",
        imageUrl: require("@/assets/images/style-quiz/swipe-img8.png"),
    },
];

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const SWIPE_THRESHOLD = 120;

const Container = styled.View`
    flex: 1;
    background-color: #fff;
    justify-content: center;
    align-items: center;
`;

const Header = styled.View`
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-horizontal: 16px;
    position: absolute;
    top: 76px;
`;

const CloseButton = styled.TouchableOpacity`
    width: 32px;
    height: 32px;
    align-items: center;
    justify-content: center;
    background-color: #F5F5F5;
    border-radius: 16px;
`;

const HeaderTitleContainer = styled.View`
    flex: 1;
    align-items: center;
    justify-content: center;
`;

const HeaderText = styled(Text)`
    font-family: CormorantGaramondSemiBold;
    font-size: 32px;
    line-height: 40px;
    letter-spacing: 0;
    color: #0E7E61;
    text-align: center;
`;

const CounterText = styled(Text)`
    font-family: MuktaVaaniLight;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0;
    color: #6D6D6D;
    text-align: left;
    width: 32px;
    height: 20px;
`;

export default function SwipeScreen() {
    const router = useRouter();
    const [currentIndex, setCurrentIndex] = useState(0);
    const [position] = useState(new Animated.ValueXY());
    const [quizQuestions, setQuizQuestions] = useState<QuizQuestion[]>(mockQuizQuestions);
    const fadeAnim = useRef(new Animated.Value(1)).current;
    const [isImageLoading, setIsImageLoading] = useState(false);

    const answerMutation = useMutation({
        mutationFn: styleQuizService.answer,
        onSuccess: () => {
            if (currentIndex + 1 >= quizQuestions.length) {
                setTimeout(() => {
                    router.push('/(style-quiz)/MoodBoardIntro');
                }, 300);
            }
        }
    });

    const handleSwipe = async (direction: "right" | "left") => {
        if (currentIndex >= quizQuestions.length) return;

        const questionId = quizQuestions[currentIndex].questionId;
        const selected = direction === "right";

        // Fade out current image with slightly longer duration
        Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 250,
            useNativeDriver: true,
        }).start();

        await answerMutation.mutateAsync({ questionId, selected });

        // If this is the last question, let the fade complete before setting index
        if (currentIndex + 1 >= quizQuestions.length) {
            // Don't update state since we're navigating away
            return;
        }

        // Reset fade animation for next image
        fadeAnim.setValue(0);
        setIsImageLoading(true);

        setCurrentIndex(currentIndex + 1);
    };

    const panResponder = PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onPanResponderMove: (_, gesture) => {
            position.setValue({ x: gesture.dx, y: gesture.dy });
        },
        onPanResponderRelease: (_, gesture) => {
            if (gesture.dx > SWIPE_THRESHOLD) {
                forceSwipe("right");
            } else if (gesture.dx < -SWIPE_THRESHOLD) {
                forceSwipe("left");
            } else {
                resetPosition();
            }
        },
    });

    const forceSwipe = (direction: "right" | "left") => {
        const x = direction === "right" ? SCREEN_WIDTH : -SCREEN_WIDTH;
        Animated.timing(position, {
            toValue: { x, y: 0 },
            duration: 300,
            useNativeDriver: true,
        }).start(() => onSwipeComplete(direction));
    };

    const onSwipeComplete = (direction: "right" | "left") => {
        handleSwipe(direction);
        position.setValue({ x: 0, y: 0 });
    };

    const resetPosition = () => {
        Animated.spring(position, {
            toValue: { x: 0, y: 0 },
            useNativeDriver: true,
        }).start();
    };

    const getCardStyle = () => {
        const rotate = position.x.interpolate({
            inputRange: [-SCREEN_WIDTH * 1.5, 0, SCREEN_WIDTH * 1.5],
            outputRange: ["-120deg", "0deg", "120deg"],
        });

        return {
            transform: [
                { translateX: position.x },
                { translateY: position.y },
                { rotate }
            ],
        };
    };

    if (currentIndex >= quizQuestions.length) {
        return null;
    }

    return (
        <Container>
            <Stack.Screen
                options={{
                    headerShown: false,
                }}
            />
            <Header>
                <CounterText>
                    {currentIndex + 1}/{quizQuestions.length}
                </CounterText>
                <HeaderTitleContainer>
                    <HeaderText>Swipe Your Style</HeaderText>
                </HeaderTitleContainer>
                <CloseButton onPress={() => router.push('/profile')}>
                    <Ionicons name="close" size={24} color="#333333" />
                </CloseButton>
            </Header>

            {/* Swipe Card with Fade Animation */}
            <Animated.View
                style={[
                    styles.card,
                    getCardStyle(),
                    { opacity: fadeAnim }
                ]}
                {...panResponder.panHandlers}
            >
                <Image
                    source={quizQuestions[currentIndex]?.imageUrl}
                    style={styles.image}
                    resizeMode="cover"
                    onLoadStart={() => setIsImageLoading(true)}
                    onLoad={() => {
                        setIsImageLoading(false);
                        Animated.timing(fadeAnim, {
                            toValue: 1,
                            duration: 200,
                            useNativeDriver: true,
                        }).start();
                    }}
                />
            </Animated.View>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
                <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => forceSwipe("left")}
                >
                    <DislikeIcon width={64} height={64} />
                </TouchableOpacity>
                <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => forceSwipe("right")}
                >
                    <LikeIcon width={64} height={64} />
                </TouchableOpacity>
            </View>
        </Container>
    );
}

const styles = StyleSheet.create({
    card: {
        width: 320,
        height: 400,
        borderRadius: 16,
        overflow: 'hidden',
    },
    image: {
        width: "100%",
        height: "100%",
    },
    actionButtons: {
        flexDirection: "row",
        justifyContent: "center",
        position: "absolute",
        bottom: 40,
        left: 0,
        right: 0,
        gap: 20,
    },
    actionButton: {
        alignItems: "center",
        justifyContent: "center",
    },
});