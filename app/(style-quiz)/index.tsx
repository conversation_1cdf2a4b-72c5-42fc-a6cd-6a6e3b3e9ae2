import React from 'react';
import { View, TouchableOpacity, SafeAreaView, Image, Platform, Text } from 'react-native';
import { Stack, router } from 'expo-router';
import styled from 'styled-components/native';
import Button from '@/components/common/Button';
import { Ionicons } from '@expo/vector-icons';

const SafeArea = styled(SafeAreaView)`
  flex: 1;
  background-color: #fff;
`;

const Container = styled(View)`
  flex: 1;
`;

const Header = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  margin-top: ${Platform.OS === 'ios' ? '0px' : '24px'};
`;

const HeaderTitle = styled(View)`
  flex: 1;
  align-items: center;
`;

const CloseButton = styled(TouchableOpacity)`
  width: 32px;
  height: 32px;
  justify-content: center;
  align-items: center;
`;

const Content = styled(View)`
  flex: 1;
  align-items: center;
  padding: 0 16px;
`;

const QuizImage = styled(Image)`
  width: 343px;
  height: 480px;
  border-radius: 16px;
  margin-top: 24px;
`;

const SubtitleContainer = styled(View)`
  padding: 24px;
  align-items: center;
`;

const ButtonContainer = styled(View)`
  width: 100%;
  padding: 16px 24px;
  margin-bottom: ${Platform.OS === 'ios' ? '8px' : '24px'};
`;

const StartButton = styled(Button)`
  width: 100%;
  height: 48px;
  background-color: #0E7E61;
  border-radius: 100px;
`;

const StyledText = styled(Text)`
  font-family: 'CormorantGaramondSemiBold';
  font-size: 32px;
  line-height: 40px;
  letter-spacing: 0px;
  text-align: center;
  color: #333333;
`;

const StyledSubtitle = styled(Text)`
  font-family: 'MuktaVaaniLight';
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  color: #333333;
  max-width: 327px;
`;

export default function StyleQuizScreen() {
  const handleClose = () => {
    router.push('/profile');
  };

  const handleStartQuiz = () => {
    // Navigate to the swipe screen
    router.push('/(style-quiz)/swipe');
  };

  return (
    <SafeArea>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <Container>
        <Header>
          <HeaderTitle>
            <StyledText>Style Quiz</StyledText>
          </HeaderTitle>
          <CloseButton onPress={handleClose}>
            <Ionicons name="close" size={24} color="#000" />
          </CloseButton>
        </Header>
        <Content>
          <QuizImage
            source={require('@/assets/images/style-quiz-intro.png')}
            resizeMode="cover"
          />
          <SubtitleContainer>
            <StyledSubtitle>
              Swipe right for styles that you'd wear and swipe left if it's not for you. Your answers will help uncover your inner style muse.
            </StyledSubtitle>
          </SubtitleContainer>
          <ButtonContainer>
            <StartButton
              onPress={handleStartQuiz}
              title="Start swiping"
              variant="filled"
              textStyle={{
                color: '#fff',
                fontSize: 16,
                fontWeight: '600',
              }}
            />
          </ButtonContainer>
        </Content>
      </Container>
    </SafeArea>
  );
} 