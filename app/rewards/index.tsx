import { RewardsGrid } from '@/components/RewardsGrid';
import HeaderPage from '@/components/common/HeaderPage';
import React from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';

export default function Rewards() {
  return (
    <View style={{ height: '100%', paddingBottom: 20 }}>
      <View>
        <HeaderPage title="Rewards" />
        <View>
          <Text style={styles.pointsEarned}>Points Earned: 100</Text>
        </View>
      </View>
      <ScrollView
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <RewardsGrid />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  pointsEarned: {
    fontFamily: 'MuktaVaaniSemiBold',
    fontSize: 12,
    fontWeight: 700,
    lineHeight: 16,
    marginBottom: 32,
    textAlign: 'center',
  },
});
