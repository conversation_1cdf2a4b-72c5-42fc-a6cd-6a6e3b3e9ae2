import DatePickerModal from '@/components/DatePickerModal';
import LocationAutocomplete from '@/components/LocationAutocomplete';
import Button from '@/components/common/Button';
import { Field, FieldGroup } from '@/components/common/Field';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import TextComponent, { TextError } from '@/components/common/Text';
import { getUserProfile, updateUserProfile } from '@/methods/users';
import { formatDate } from '@/utils/tripHelpers';
import { useQueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useTheme } from 'styled-components';

type Location = {
  name: string;
  latitude: number;
  longitude: number;
};

const PersonalInformation = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [location, setLocation] = useState<Location>({
    name: '',
    latitude: 0,
    longitude: 0,
  });
  const [birthDate, setBirthDate] = useState(new Date());
  const [errorMessage, setErrorMessage] = useState('');

  const [showDatePicker, setShowDatePicker] = useState(false);

  const { data: userProfile, isLoading: isLoadingProfile } = getUserProfile();
  const {
    mutate: updateProfile,
    isPending: isUpdatingProfile,
    isSuccess: isUpdateSuccess,
  } = updateUserProfile();

  const queryClient = useQueryClient();

  const theme = useTheme();

  useEffect(() => {
    if (userProfile) {
      setName(userProfile.data?.profile?.name);
      setEmail(userProfile.data?.profile?.email);
      setLocation({
        name: userProfile.data?.profile?.cityName,
        latitude: userProfile.data?.profile?.latitude,
        longitude: userProfile.data?.profile?.longitude,
      });
      setBirthDate(userProfile.data?.profile?.birthDate);
    }
  }, []);

  const openDatePicker = useCallback(() => {
    setShowDatePicker(true);
  }, []);

  const handleBirthdayChange = useCallback((selectedDate: Date) => {
    setBirthDate(selectedDate);
    setShowDatePicker(false);
  }, []);

  const handleCityChange = useCallback(
    (city: { name: string; latitude: number; longitude: number }) => {
      setLocation({
        name: city.name,
        latitude: city.latitude,
        longitude: city.longitude,
      });
    },
    [],
  );

  const handleSaveChanges = useCallback(() => {
    if (!name) {
      setErrorMessage('Name is required');
      return;
    }

    if (
      !location.name ||
      !location.latitude ||
      !location.longitude ||
      location.latitude === 0 ||
      location.longitude === 0
    ) {
      setErrorMessage('Invalid city');
      return;
    }

    if (!birthDate) {
      setErrorMessage('Birthday is required');
      return;
    }

    updateProfile({
      name,
      cityName: location.name,
      latitude: location.latitude,
      longitude: location.longitude,
      birthDate,
    });
  }, [name, location, birthDate, updateProfile]);

  useEffect(() => {
    if (isUpdateSuccess) {
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      router.back();
    }
  }, [isUpdateSuccess]);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage noLogo />
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Personal Information" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <TextComponent string="Change your personal information so other people will recognize you." />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <FieldGroup>
            <Field label="Name">
              <TextInput
                style={[styles.textInput]}
                placeholder="Enter name"
                value={name}
                onChangeText={(text) => setName(text)}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field label="Email">
              <TextInput
                style={[styles.textInput]}
                value={email}
                editable={false}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field label="Home City">
              <View
                style={{
                  width: '60%',
                  position: 'relative',
                  zIndex: 1,
                  height: 30,
                }}
              >
                <LocationAutocomplete
                  value={location.name}
                  onLocationSelect={(loc) => handleCityChange(loc)}
                />
              </View>
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field label="Birthday">
              <TouchableOpacity
                style={{
                  backgroundColor: theme.brand.green[500],
                  paddingVertical: 4,
                  paddingHorizontal: 8,
                  borderRadius: 4,
                }}
                onPress={openDatePicker}
              >
                <Text
                  style={{
                    color: '#fff',
                    fontSize: 16,
                    textAlign: 'center',
                  }}
                >
                  {birthDate ? formatDate(birthDate) : 'Select birthday'}
                </Text>
              </TouchableOpacity>
            </Field>
          </FieldGroup>

          {errorMessage ? <TextError string={errorMessage} /> : null}
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          title="Save changes"
          isDisabled={isLoadingProfile || isUpdatingProfile}
          onPress={handleSaveChanges}
        />
      </View>
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateChange={handleBirthdayChange}
        value={birthDate || new Date()}
        title="Select End Date"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  textInput: {
    height: 30,
    width: '60%',
    fontFamily: 'MuktaVaaniLight',
    fontSize: 16,
    padding: 0,
    textAlign: 'right',
  },
});

export default PersonalInformation;
