import CalendarList from '@/components/CalendarList';
import CalendarSelection from '@/components/CalendarSelection';
import HeaderDashboard from '@/components/common/HeaderDashboard';
import TagLocation from '@/components/common/TagLocation';
import TodayComponent from '@/components/common/TodayComponent';
import UpcomingTrips from '@/components/UpcomingTrips';
import { useSession } from '@/config/ctx';
import { getUserProfile } from '@/methods/users';
import { useRouter } from 'expo-router';
import { ScrollView, View } from 'react-native';

export default function HomeScreen() {
  const { signOut } = useSession();
  const router = useRouter();
  const { data: userProfile, isLoading } = getUserProfile();

  // useEffect(() => {
  // if (userProfile && !userProfile?.data?.profile?.isOnboardingCompleted) {
  //   setTimeout(() => {
  //     router.push('/user-info/gender');
  //   }, 2000);
  // }
  // }, []);

  const profile = userProfile?.data.profile;
  const firstName = profile?.name ? profile.name.split(' ')[0] : '';
  const displayName = firstName || profile?.userName || profile?.email;

  return (
    <View>
      <HeaderDashboard title={`Welcome, ${displayName}!`} />
      <ScrollView
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={{ paddingBottom: 200 }}
      >
        {/* <TagLocation /> */}
        <TodayComponent />
        <CalendarSelection />
        <CalendarList />
        <UpcomingTrips />
      </ScrollView>
    </View>
  );
}
