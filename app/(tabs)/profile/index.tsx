import { InviteCard } from '@/components/InviteCard';
import { PlanCard } from '@/components/PlanCard';
import { ProfileHeader } from '@/components/ProfileHeader';
import { QuizCard } from '@/components/QuizCard';
import { RewardsSection } from '@/components/RewardsSection';
import { StyleProfileCard } from '@/components/StyleProfileCard';
import { ScrollView, StyleSheet, View } from 'react-native';

export default function Profile() {
  return (
    <ScrollView
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <ProfileHeader />
      <View style={styles.container}>
        <View style={styles.inner}>
          <PlanCard />
          <StyleProfileCard hasStyleProfile={false} />
          <QuizCard />
          <RewardsSection />
          <InviteCard />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingTop: 24,
  },
  inner: {
    flex: 1,
    gap: 24,
    width: '90%',
    paddingBottom: 20,
  },
});
